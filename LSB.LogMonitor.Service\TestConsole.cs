using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using LSB.LogMonitor.Services;
using LSB.LogMonitor.Service.Models;

namespace LSB.LogMonitor.Service
{
    public class TestConsole
    {
        public static async Task Main(string[] args)
        {
            Console.WriteLine("🔧 LSB Log Monitor Service Test Console");
            Console.WriteLine("=====================================");

            // Build configuration
            var configuration = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                .Build();

            // Setup DI container
            var services = new ServiceCollection();
            services.AddSingleton<IConfiguration>(configuration);
            services.AddLogging(builder =>
            {
                builder.AddConsole();
                builder.SetMinimumLevel(LogLevel.Information);
            });

            // Configure options
            services.Configure<LogMonitorOptions>(configuration.GetSection(LogMonitorOptions.SectionName));
            services.AddSingleton<ILogService, LogService>();
            services.AddSingleton<ITelegramService, TelegramService>();

            var serviceProvider = services.BuildServiceProvider();

            var logger = serviceProvider.GetRequiredService<ILogger<TestConsole>>();
            var logService = serviceProvider.GetRequiredService<ILogService>();
            var telegramService = serviceProvider.GetRequiredService<ITelegramService>();

            try
            {
                Console.WriteLine("\n1. Testing Telegram Service...");
                await telegramService.SendTestMessageAsync();
                Console.WriteLine("✅ Telegram test message sent successfully!");

                Console.WriteLine("\n2. Testing Log Service...");
                var clientNames = await logService.GetAvailableClientNamesAsync();
                Console.WriteLine($"📋 Found {clientNames.Length} clients: {string.Join(", ", clientNames)}");

                var today = DateTime.Today;
                Console.WriteLine($"\n3. Checking logs for date: {today:yyyy-MM-dd}");

                var clientsWithLogs = new List<ClientLogInfo>();

                foreach (var clientName in clientNames)
                {
                    Console.WriteLine($"\n   Checking client: {clientName}");
                    var logsExist = await logService.LogsExistAsync(today, clientName);
                    Console.WriteLine($"   Logs exist: {logsExist}");

                    if (logsExist)
                    {
                        var summary = await logService.GenerateLogSummaryAsync(today, clientName);
                        Console.WriteLine($"   📁 Files: {summary.LogFiles.Count}");
                        Console.WriteLine($"   📝 Entries: {summary.TotalLogEntries}");
                        Console.WriteLine($"   🔴 Errors: {summary.HealthSummary?.TotalErrors ?? 0}");

                        clientsWithLogs.Add(new ClientLogInfo
                        {
                            ClientName = clientName,
                            LogFileCount = summary.LogFiles.Count,
                            Summary = summary,
                            Date = today
                        });
                    }
                }

                Console.WriteLine($"\n4. Total clients with logs: {clientsWithLogs.Count}");

                if (clientsWithLogs.Any())
                {
                    Console.WriteLine("\n5. Sending log notification...");
                    await telegramService.SendLogNotificationAsync(clientsWithLogs, today);
                    Console.WriteLine("✅ Log notification sent successfully!");
                }
                else
                {
                    Console.WriteLine("ℹ️ No logs found to send notification");
                }

                Console.WriteLine("\n🎉 All tests completed successfully!");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Test failed");
                Console.WriteLine($"❌ Test failed: {ex.Message}");
            }

            Console.WriteLine("\nPress any key to exit...");
            Console.ReadKey();
        }
    }
}
