﻿<?xml version="1.0" encoding="utf-8"?><wixPdb version="3.0.3200.0" xmlns="http://schemas.microsoft.com/wix/2006/pdbs"><wixOutput type="Product" codepage="1252" version="3.0.2002.0" xmlns="http://schemas.microsoft.com/wix/2006/outputs"><tableDefinitions xmlns="http://schemas.microsoft.com/wix/2006/tables"><tableDefinition name="_Streams" unreal="yes"><columnDefinition name="Name" type="string" length="62" primaryKey="yes" /><columnDefinition name="Data" type="object" length="0" nullable="yes" /></tableDefinition><tableDefinition name="_SummaryInformation"><columnDefinition name="PropertyId" type="number" length="2" primaryKey="yes" /><columnDefinition name="Value" type="localized" length="255" escapeIdtCharacters="yes" /></tableDefinition><tableDefinition name="_Validation"><columnDefinition name="Table" type="string" length="32" primaryKey="yes" category="identifier" description="Name of table" /><columnDefinition name="Column" type="string" length="32" primaryKey="yes" category="identifier" description="Name of column" /><columnDefinition name="Nullable" type="string" length="4" set="Y;N" description="Whether the column is nullable" /><columnDefinition name="MinValue" type="number" length="4" nullable="yes" minValue="-2147483647" maxValue="2147483647" description="Minimum value allowed" /><columnDefinition name="MaxValue" type="number" length="4" nullable="yes" minValue="-2147483647" maxValue="2147483647" description="Maximum value allowed" /><columnDefinition name="KeyTable" type="string" length="255" nullable="yes" category="identifier" description="For foreign key, Name of table to which data must link" /><columnDefinition name="KeyColumn" type="number" length="2" nullable="yes" minValue="1" maxValue="32" description="Column to which foreign key connects" /><columnDefinition name="Category" type="string" length="32" nullable="yes" set="Text;Formatted;Template;Condition;Guid;Path;Version;Language;Identifier;Binary;UpperCase;LowerCase;Filename;Paths;AnyPath;WildCardFilename;RegPath;CustomSource;Property;Cabinet;Shortcut;FormattedSDDLText;Integer;DoubleInteger;TimeDate;DefaultDir" description="String category" /><columnDefinition name="Set" type="string" length="255" nullable="yes" category="text" description="Set of values that are permitted" /><columnDefinition name="Description" type="string" length="255" nullable="yes" category="text" description="Description of column" /></tableDefinition><tableDefinition name="AdminExecuteSequence"><columnDefinition name="Action" type="string" length="72" primaryKey="yes" category="identifier" description="Name of action to invoke, either in the engine or the handler DLL." /><columnDefinition name="Condition" type="string" length="255" nullable="yes" localizable="yes" category="condition" description="Optional expression which skips the action if evaluates to expFalse.If the expression syntax is invalid, the engine will terminate, returning iesBadActionData." /><columnDefinition name="Sequence" type="number" length="2" nullable="yes" minValue="-4" maxValue="32767" description="Number that determines the sort order in which the actions are to be executed.  Leave blank to suppress action." /></tableDefinition><tableDefinition name="AdminUISequence"><columnDefinition name="Action" type="string" length="72" primaryKey="yes" category="identifier" description="Name of action to invoke, either in the engine or the handler DLL." /><columnDefinition name="Condition" type="string" length="255" nullable="yes" localizable="yes" category="condition" description="Optional expression which skips the action if evaluates to expFalse.If the expression syntax is invalid, the engine will terminate, returning iesBadActionData." /><columnDefinition name="Sequence" type="number" length="2" nullable="yes" minValue="-4" maxValue="32767" description="Number that determines the sort order in which the actions are to be executed.  Leave blank to suppress action." /></tableDefinition><tableDefinition name="AdvtExecuteSequence"><columnDefinition name="Action" type="string" length="72" primaryKey="yes" category="identifier" description="Name of action to invoke, either in the engine or the handler DLL." /><columnDefinition name="Condition" type="string" length="255" nullable="yes" localizable="yes" category="condition" description="Optional expression which skips the action if evaluates to expFalse.If the expression syntax is invalid, the engine will terminate, returning iesBadActionData." /><columnDefinition name="Sequence" type="number" length="2" nullable="yes" minValue="-4" maxValue="32767" description="Number that determines the sort order in which the actions are to be executed.  Leave blank to suppress action." /></tableDefinition><tableDefinition name="Component" createSymbols="yes"><columnDefinition name="Component" type="string" length="72" primaryKey="yes" modularize="column" category="identifier" description="Primary key used to identify a particular component record." /><columnDefinition name="ComponentId" type="string" length="38" nullable="yes" category="guid" description="A string GUID unique to this component, version, and language." /><columnDefinition name="Directory_" type="string" length="72" modularize="column" keyTable="Directory" keyColumn="1" category="identifier" description="Required key of a Directory table record. This is actually a property name whose value contains the actual path, set either by the AppSearch action or with the default setting obtained from the Directory table." /><columnDefinition name="Attributes" type="number" length="2" description="Remote execution option, one of irsEnum" /><columnDefinition name="Condition" type="string" length="255" nullable="yes" localizable="yes" modularize="condition" category="condition" description="A conditional statement that will disable this component if the specified condition evaluates to the 'True' state. If a component is disabled, it will not be installed, regardless of the 'Action' state associated with the component." /><columnDefinition name="KeyPath" type="string" length="72" nullable="yes" modularize="column" keyTable="File;Registry;ODBCDataSource" keyColumn="1" category="identifier" description="Either the primary key into the File table, Registry table, or ODBCDataSource table. This extract path is stored when the component is installed, and is used to detect the presence of the component and to return the path to it." /></tableDefinition><tableDefinition name="CustomAction" createSymbols="yes"><columnDefinition name="Action" type="string" length="72" primaryKey="yes" modularize="column" category="identifier" description="Primary key, name of action, normally appears in sequence table unless private use." /><columnDefinition name="Type" type="number" length="2" minValue="1" maxValue="32767" description="The numeric custom action type, consisting of source location, code type, entry, option flags." /><columnDefinition name="Source" type="string" length="72" nullable="yes" modularize="column" category="customSource" description="The table reference of the source of the code." /><columnDefinition name="Target" type="string" length="255" nullable="yes" localizable="yes" modularize="property" category="formatted" description="Excecution parameter, depends on the type of custom action" escapeIdtCharacters="yes" /><columnDefinition name="ExtendedType" type="number" length="4" nullable="yes" minValue="0" maxValue="2147483647" description="A numeric custom action type that extends code type or option flags of the Type column." /></tableDefinition><tableDefinition name="Directory" createSymbols="yes"><columnDefinition name="Directory" type="string" length="72" primaryKey="yes" modularize="column" category="identifier" description="Unique identifier for directory entry, primary key. If a property by this name is defined, it contains the full path to the directory." /><columnDefinition name="Directory_Parent" type="string" length="72" nullable="yes" modularize="column" keyTable="Directory" keyColumn="1" category="identifier" description="Reference to the entry in this table specifying the default parent directory. A record parented to itself or with a Null parent represents a root of the install tree." /><columnDefinition name="DefaultDir" type="localized" length="255" category="defaultDir" description="The default sub-path under parent's path." /></tableDefinition><tableDefinition name="Feature" createSymbols="yes"><columnDefinition name="Feature" type="string" length="38" primaryKey="yes" category="identifier" description="Primary key used to identify a particular feature record." /><columnDefinition name="Feature_Parent" type="string" length="38" nullable="yes" keyTable="Feature" keyColumn="1" category="identifier" description="Optional key of a parent record in the same table. If the parent is not selected, then the record will not be installed. Null indicates a root item." /><columnDefinition name="Title" type="localized" length="64" nullable="yes" category="text" description="Short text identifying a visible feature item." escapeIdtCharacters="yes" /><columnDefinition name="Description" type="localized" length="255" nullable="yes" category="text" description="Longer descriptive text describing a visible feature item." escapeIdtCharacters="yes" /><columnDefinition name="Display" type="number" length="2" nullable="yes" minValue="0" maxValue="32767" description="Numeric sort order, used to force a specific display ordering." /><columnDefinition name="Level" type="number" length="2" minValue="0" maxValue="32767" description="The install level at which record will be initially selected. An install level of 0 will disable an item and prevent its display." /><columnDefinition name="Directory_" type="string" length="72" nullable="yes" modularize="column" keyTable="Directory" keyColumn="1" category="upperCase" description="The name of the Directory that can be configured by the UI. A non-null value will enable the browse button." /><columnDefinition name="Attributes" type="number" length="2" set="0;1;2;4;5;6;8;9;10;16;17;18;20;21;22;24;25;26;32;33;34;36;37;38;48;49;50;52;53;54" description="Feature attributes" /></tableDefinition><tableDefinition name="FeatureComponents"><columnDefinition name="Feature_" type="string" length="38" primaryKey="yes" keyTable="Feature" keyColumn="1" category="identifier" description="Foreign key into Feature table." /><columnDefinition name="Component_" type="string" length="72" primaryKey="yes" modularize="column" keyTable="Component" keyColumn="1" category="identifier" description="Foreign key into Component table." /></tableDefinition><tableDefinition name="File" createSymbols="yes"><columnDefinition name="File" type="string" length="72" primaryKey="yes" modularize="column" category="identifier" description="Primary key, non-localized token, must match identifier in cabinet.  For uncompressed files, this field is ignored." /><columnDefinition name="Component_" type="string" length="72" modularize="column" keyTable="Component" keyColumn="1" category="identifier" description="Foreign key referencing Component that controls the file." /><columnDefinition name="FileName" type="localized" length="255" category="filename" description="File name used for installation, may be localized.  This may contain a &quot;short name|long name&quot; pair." /><columnDefinition name="FileSize" type="number" length="4" minValue="0" maxValue="2147483647" description="Size of file in bytes (long integer)." /><columnDefinition name="Version" type="string" length="72" nullable="yes" modularize="companionFile" keyTable="File" keyColumn="1" category="version" description="Version string for versioned files;  Blank for unversioned files." /><columnDefinition name="Language" type="string" length="20" nullable="yes" category="language" description="List of decimal language Ids, comma-separated if more than one." /><columnDefinition name="Attributes" type="number" length="2" nullable="yes" minValue="0" maxValue="32767" description="Integer containing bit flags representing file attributes (with the decimal value of each bit position in parentheses)" /><columnDefinition name="Sequence" type="number" length="4" minValue="1" maxValue="2147483647" description="Sequence with respect to the media images; order must track cabinet order." /></tableDefinition><tableDefinition name="InstallExecuteSequence"><columnDefinition name="Action" type="string" length="72" primaryKey="yes" category="identifier" description="Name of action to invoke, either in the engine or the handler DLL." /><columnDefinition name="Condition" type="string" length="255" nullable="yes" localizable="yes" category="condition" description="Optional expression which skips the action if evaluates to expFalse.If the expression syntax is invalid, the engine will terminate, returning iesBadActionData." /><columnDefinition name="Sequence" type="number" length="2" nullable="yes" minValue="-4" maxValue="32767" description="Number that determines the sort order in which the actions are to be executed.  Leave blank to suppress action." /></tableDefinition><tableDefinition name="InstallUISequence"><columnDefinition name="Action" type="string" length="72" primaryKey="yes" category="identifier" description="Name of action to invoke, either in the engine or the handler DLL." /><columnDefinition name="Condition" type="string" length="255" nullable="yes" localizable="yes" category="condition" description="Optional expression which skips the action if evaluates to expFalse.If the expression syntax is invalid, the engine will terminate, returning iesBadActionData." /><columnDefinition name="Sequence" type="number" length="2" nullable="yes" minValue="-4" maxValue="32767" description="Number that determines the sort order in which the actions are to be executed.  Leave blank to suppress action." /></tableDefinition><tableDefinition name="LaunchCondition"><columnDefinition name="Condition" type="string" length="255" primaryKey="yes" localizable="yes" category="condition" description="Expression which must evaluate to TRUE in order for install to commence." /><columnDefinition name="Description" type="localized" length="255" category="formatted" description="Localizable text to display when condition fails and install must abort." escapeIdtCharacters="yes" /></tableDefinition><tableDefinition name="Media" createSymbols="yes"><columnDefinition name="DiskId" type="number" length="2" primaryKey="yes" minValue="1" maxValue="32767" description="Primary key, integer to determine sort order for table." /><columnDefinition name="LastSequence" type="number" length="4" minValue="0" maxValue="2147483647" description="File sequence number for the last file for this media." /><columnDefinition name="DiskPrompt" type="localized" length="64" nullable="yes" category="text" description="Disk name: the visible text actually printed on the disk.  This will be used to prompt the user when this disk needs to be inserted." escapeIdtCharacters="yes" /><columnDefinition name="Cabinet" type="string" length="255" nullable="yes" category="cabinet" description="If some or all of the files stored on the media are compressed in a cabinet, the name of that cabinet." /><columnDefinition name="VolumeLabel" type="string" length="32" nullable="yes" category="text" description="The label attributed to the volume." /><columnDefinition name="Source" type="string" length="72" nullable="yes" category="property" description="The property defining the location of the cabinet file." /></tableDefinition><tableDefinition name="MsiFileHash"><columnDefinition name="File_" type="string" length="72" primaryKey="yes" modularize="column" keyTable="File" keyColumn="1" category="identifier" description="Primary key, foreign key into File table referencing file with this hash" /><columnDefinition name="Options" type="number" length="2" minValue="0" maxValue="32767" description="Various options and attributes for this hash." /><columnDefinition name="HashPart1" type="number" length="4" description="Size of file in bytes (long integer)." /><columnDefinition name="HashPart2" type="number" length="4" description="Size of file in bytes (long integer)." /><columnDefinition name="HashPart3" type="number" length="4" description="Size of file in bytes (long integer)." /><columnDefinition name="HashPart4" type="number" length="4" description="Size of file in bytes (long integer)." /></tableDefinition><tableDefinition name="Property" createSymbols="yes"><columnDefinition name="Property" type="string" length="72" primaryKey="yes" modularize="column" category="identifier" description="Name of property, uppercase if settable by launcher or loader." /><columnDefinition name="Value" type="localized" length="0" category="text" description="String value for property.  Never null or empty." escapeIdtCharacters="yes" /></tableDefinition><tableDefinition name="ServiceControl"><columnDefinition name="ServiceControl" type="string" length="72" primaryKey="yes" modularize="column" category="identifier" description="Primary key, non-localized token." /><columnDefinition name="Name" type="localized" length="255" modularize="property" category="formatted" description="Name of a service. /, \, comma and space are invalid" /><columnDefinition name="Event" type="number" length="2" minValue="0" maxValue="187" description="Bit field:  Install:  0x1 = Start, 0x2 = Stop, 0x8 = Delete, Uninstall: 0x10 = Start, 0x20 = Stop, 0x80 = Delete" /><columnDefinition name="Arguments" type="localized" length="255" nullable="yes" modularize="property" category="formatted" description="Arguments for the service.  Separate by [~]." /><columnDefinition name="Wait" type="number" length="2" nullable="yes" minValue="0" maxValue="1" description="Boolean for whether to wait for the service to fully start" /><columnDefinition name="Component_" type="string" length="72" modularize="column" keyTable="Component" keyColumn="1" category="identifier" description="Required foreign key into the Component Table that controls the startup of the service" /></tableDefinition><tableDefinition name="ServiceInstall"><columnDefinition name="ServiceInstall" type="string" length="72" primaryKey="yes" modularize="column" category="identifier" description="Primary key, non-localized token." /><columnDefinition name="Name" type="string" length="255" modularize="property" category="formatted" description="Internal Name of the Service" /><columnDefinition name="DisplayName" type="localized" length="255" nullable="yes" modularize="property" category="formatted" description="External Name of the Service" escapeIdtCharacters="yes" /><columnDefinition name="ServiceType" type="number" length="4" minValue="-2147483647" maxValue="2147483647" description="Type of the service" /><columnDefinition name="StartType" type="number" length="4" minValue="0" maxValue="4" description="Type of the service" /><columnDefinition name="ErrorControl" type="number" length="4" minValue="-2147483647" maxValue="2147483647" description="Severity of error if service fails to start" /><columnDefinition name="LoadOrderGroup" type="string" length="255" nullable="yes" modularize="property" category="formatted" description="LoadOrderGroup" /><columnDefinition name="Dependencies" type="string" length="255" nullable="yes" modularize="property" category="formatted" description="Other services this depends on to start.  Separate by [~], and end with [~][~]" /><columnDefinition name="StartName" type="string" length="255" nullable="yes" modularize="property" category="formatted" description="User or object name to run service as" /><columnDefinition name="Password" type="string" length="255" nullable="yes" modularize="property" category="formatted" description="password to run service with.  (with StartName)" /><columnDefinition name="Arguments" type="string" length="255" nullable="yes" modularize="property" category="formatted" description="Arguments to include in every start of the service, passed to WinMain" /><columnDefinition name="Component_" type="string" length="72" modularize="column" keyTable="Component" keyColumn="1" category="identifier" description="Required foreign key into the Component Table that controls the startup of the service" /><columnDefinition name="Description" type="localized" length="255" nullable="yes" modularize="property" category="text" description="Description of service." escapeIdtCharacters="yes" /></tableDefinition><tableDefinition name="Upgrade"><columnDefinition name="UpgradeCode" type="string" length="38" primaryKey="yes" category="guid" description="The UpgradeCode GUID belonging to the products in this set." /><columnDefinition name="VersionMin" type="string" length="20" primaryKey="yes" nullable="yes" category="text" description="The minimum ProductVersion of the products in this set.  The set may or may not include products with this particular version." /><columnDefinition name="VersionMax" type="string" length="20" primaryKey="yes" nullable="yes" category="text" description="The maximum ProductVersion of the products in this set.  The set may or may not include products with this particular version." /><columnDefinition name="Language" type="string" length="255" primaryKey="yes" nullable="yes" localizable="yes" category="language" description="A comma-separated list of languages for either products in this set or products not in this set." /><columnDefinition name="Attributes" type="number" length="4" primaryKey="yes" minValue="0" maxValue="2147483647" description="The attributes of this product set." /><columnDefinition name="Remove" type="string" length="255" nullable="yes" category="formatted" description="The list of features to remove when uninstalling a product from this set.  The default is &quot;ALL&quot;." /><columnDefinition name="ActionProperty" type="string" length="72" category="upperCase" description="The property to set when a product in this set is found." /></tableDefinition><tableDefinition name="WixAction" createSymbols="yes" unreal="yes"><columnDefinition name="SequenceTable" type="string" length="62" primaryKey="yes" /><columnDefinition name="Action" type="string" length="72" primaryKey="yes" /><columnDefinition name="Condition" type="string" length="255" nullable="yes" localizable="yes" /><columnDefinition name="Sequence" type="number" length="2" nullable="yes" /><columnDefinition name="Before" type="string" length="72" nullable="yes" /><columnDefinition name="After" type="string" length="72" nullable="yes" /><columnDefinition name="Overridable" type="number" length="2" nullable="yes" /></tableDefinition><tableDefinition name="WixBuildInfo" unreal="yes"><columnDefinition name="WixVersion" type="string" length="20" category="text" description="Version number of WiX." /><columnDefinition name="WixOutputFile" type="string" length="0" nullable="yes" category="text" description="Path to output file, if supplied." escapeIdtCharacters="yes" /><columnDefinition name="WixProjectFile" type="string" length="0" nullable="yes" category="text" description="Path to .wixproj file, if supplied." escapeIdtCharacters="yes" /><columnDefinition name="WixPdbFile" type="string" length="0" nullable="yes" category="text" description="Path to .wixpdb file, if supplied." escapeIdtCharacters="yes" /></tableDefinition><tableDefinition name="WixComplexReference" unreal="yes"><columnDefinition name="Parent" type="string" length="0" localizable="yes" /><columnDefinition name="ParentAttributes" type="number" length="4" /><columnDefinition name="ParentLanguage" type="string" length="0" nullable="yes" /><columnDefinition name="Child" type="string" length="0" localizable="yes" /><columnDefinition name="ChildAttributes" type="number" length="4" /><columnDefinition name="Attributes" type="number" length="4" /></tableDefinition><tableDefinition name="WixComponentGroup" createSymbols="yes" unreal="yes"><columnDefinition name="WixComponentGroup" type="string" length="0" primaryKey="yes" /></tableDefinition><tableDefinition name="WixFile" unreal="yes"><columnDefinition name="File_" type="string" length="0" primaryKey="yes" modularize="column" keyTable="File" keyColumn="1" /><columnDefinition name="AssemblyAttributes" type="number" length="4" nullable="yes" /><columnDefinition name="File_AssemblyManifest" type="string" length="72" nullable="yes" modularize="column" /><columnDefinition name="File_AssemblyApplication" type="string" length="72" nullable="yes" modularize="column" /><columnDefinition name="Directory_" type="string" length="72" /><columnDefinition name="DiskId" type="number" length="4" nullable="yes" /><columnDefinition name="Source" type="object" length="0" /><columnDefinition name="ProcessorArchitecture" type="string" length="0" nullable="yes" /><columnDefinition name="PatchGroup" type="number" length="4" /><columnDefinition name="Attributes" type="number" length="4" /><columnDefinition name="PatchAttributes" type="number" length="4" nullable="yes" /><columnDefinition name="RetainLengths" type="preserved" length="0" nullable="yes" category="text" /><columnDefinition name="IgnoreOffsets" type="preserved" length="0" nullable="yes" category="text" /><columnDefinition name="IgnoreLengths" type="preserved" length="0" nullable="yes" category="text" /><columnDefinition name="RetainOffsets" type="preserved" length="0" nullable="yes" category="text" /></tableDefinition><tableDefinition name="WixGroup" unreal="yes"><columnDefinition name="ParentId" type="string" length="0" primaryKey="yes" category="identifier" description="Primary key used to identify a particular record in a parent table." /><columnDefinition name="ParentType" type="string" length="0" primaryKey="yes" description="Primary key used to identify a particular parent type in a parent table." /><columnDefinition name="ChildId" type="string" length="0" primaryKey="yes" category="identifier" description="Primary key used to identify a particular record in a child table." /><columnDefinition name="ChildType" type="string" length="0" primaryKey="yes" description="Primary key used to identify a particular child type in a child table." /></tableDefinition><tableDefinition name="WixSimpleReference" unreal="yes"><columnDefinition name="Table" type="string" length="32" /><columnDefinition name="PrimaryKeys" type="string" length="0" /></tableDefinition></tableDefinitions><table name="_Streams" xmlns="http://schemas.microsoft.com/wix/2006/objects" /><table name="_SummaryInformation" xmlns="http://schemas.microsoft.com/wix/2006/objects"><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*12"><field>1</field><field>1252</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*12"><field>2</field><field>Installation Database</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*12"><field>3</field><field>LSB Telemetry Service</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*12"><field>4</field><field>LSB</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*12"><field>5</field><field>Installer</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*12"><field>6</field><field>This installer database contains the logic and data required to install LSB Telemetry Service.</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*12"><field>7</field><field>x64;1033</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*12"><field>9</field><field>{6AAF65DC-DB5B-4E7C-9886-ED31B6D0A49B}</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*12"><field>14</field><field>200</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*12"><field>15</field><field>2</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*12"><field>19</field><field>2</field></row><row><field>12</field><field>2025/06/02 20:49:00</field></row><row><field>13</field><field>2025/06/02 20:49:00</field></row><row><field>18</field><field>Windows Installer XML Toolset (3.14.1.8722)</field></row></table><table name="_Validation" xmlns="http://schemas.microsoft.com/wix/2006/objects"><row><field>_SummaryInformation</field><field>PropertyId</field><field>N</field><field /><field /><field /><field /><field /><field /><field /></row><row><field>_SummaryInformation</field><field>Value</field><field>N</field><field /><field /><field /><field /><field /><field /><field /></row><row><field>_Validation</field><field>Table</field><field>N</field><field /><field /><field /><field /><field>Identifier</field><field /><field>Name of table</field></row><row><field>_Validation</field><field>Column</field><field>N</field><field /><field /><field /><field /><field>Identifier</field><field /><field>Name of column</field></row><row><field>_Validation</field><field>Nullable</field><field>N</field><field /><field /><field /><field /><field /><field>Y;N</field><field>Whether the column is nullable</field></row><row><field>_Validation</field><field>MinValue</field><field>Y</field><field>-2147483647</field><field>2147483647</field><field /><field /><field /><field /><field>Minimum value allowed</field></row><row><field>_Validation</field><field>MaxValue</field><field>Y</field><field>-2147483647</field><field>2147483647</field><field /><field /><field /><field /><field>Maximum value allowed</field></row><row><field>_Validation</field><field>KeyTable</field><field>Y</field><field /><field /><field /><field /><field>Identifier</field><field /><field>For foreign key, Name of table to which data must link</field></row><row><field>_Validation</field><field>KeyColumn</field><field>Y</field><field>1</field><field>32</field><field /><field /><field /><field /><field>Column to which foreign key connects</field></row><row><field>_Validation</field><field>Category</field><field>Y</field><field /><field /><field /><field /><field /><field>Text;Formatted;Template;Condition;Guid;Path;Version;Language;Identifier;Binary;UpperCase;LowerCase;Filename;Paths;AnyPath;WildCardFilename;RegPath;CustomSource;Property;Cabinet;Shortcut;FormattedSDDLText;Integer;DoubleInteger;TimeDate;DefaultDir</field><field>String category</field></row><row><field>_Validation</field><field>Set</field><field>Y</field><field /><field /><field /><field /><field>Text</field><field /><field>Set of values that are permitted</field></row><row><field>_Validation</field><field>Description</field><field>Y</field><field /><field /><field /><field /><field>Text</field><field /><field>Description of column</field></row><row><field>AdminExecuteSequence</field><field>Action</field><field>N</field><field /><field /><field /><field /><field>Identifier</field><field /><field>Name of action to invoke, either in the engine or the handler DLL.</field></row><row><field>AdminExecuteSequence</field><field>Condition</field><field>Y</field><field /><field /><field /><field /><field>Condition</field><field /><field>Optional expression which skips the action if evaluates to expFalse.If the expression syntax is invalid, the engine will terminate, returning iesBadActionData.</field></row><row><field>AdminExecuteSequence</field><field>Sequence</field><field>Y</field><field>-4</field><field>32767</field><field /><field /><field /><field /><field>Number that determines the sort order in which the actions are to be executed.  Leave blank to suppress action.</field></row><row><field>AdminUISequence</field><field>Action</field><field>N</field><field /><field /><field /><field /><field>Identifier</field><field /><field>Name of action to invoke, either in the engine or the handler DLL.</field></row><row><field>AdminUISequence</field><field>Condition</field><field>Y</field><field /><field /><field /><field /><field>Condition</field><field /><field>Optional expression which skips the action if evaluates to expFalse.If the expression syntax is invalid, the engine will terminate, returning iesBadActionData.</field></row><row><field>AdminUISequence</field><field>Sequence</field><field>Y</field><field>-4</field><field>32767</field><field /><field /><field /><field /><field>Number that determines the sort order in which the actions are to be executed.  Leave blank to suppress action.</field></row><row><field>AdvtExecuteSequence</field><field>Action</field><field>N</field><field /><field /><field /><field /><field>Identifier</field><field /><field>Name of action to invoke, either in the engine or the handler DLL.</field></row><row><field>AdvtExecuteSequence</field><field>Condition</field><field>Y</field><field /><field /><field /><field /><field>Condition</field><field /><field>Optional expression which skips the action if evaluates to expFalse.If the expression syntax is invalid, the engine will terminate, returning iesBadActionData.</field></row><row><field>AdvtExecuteSequence</field><field>Sequence</field><field>Y</field><field>-4</field><field>32767</field><field /><field /><field /><field /><field>Number that determines the sort order in which the actions are to be executed.  Leave blank to suppress action.</field></row><row><field>Component</field><field>Component</field><field>N</field><field /><field /><field /><field /><field>Identifier</field><field /><field>Primary key used to identify a particular component record.</field></row><row><field>Component</field><field>ComponentId</field><field>Y</field><field /><field /><field /><field /><field>Guid</field><field /><field>A string GUID unique to this component, version, and language.</field></row><row><field>Component</field><field>Directory_</field><field>N</field><field /><field /><field>Directory</field><field>1</field><field>Identifier</field><field /><field>Required key of a Directory table record. This is actually a property name whose value contains the actual path, set either by the AppSearch action or with the default setting obtained from the Directory table.</field></row><row><field>Component</field><field>Attributes</field><field>N</field><field /><field /><field /><field /><field /><field /><field>Remote execution option, one of irsEnum</field></row><row><field>Component</field><field>Condition</field><field>Y</field><field /><field /><field /><field /><field>Condition</field><field /><field>A conditional statement that will disable this component if the specified condition evaluates to the 'True' state. If a component is disabled, it will not be installed, regardless of the 'Action' state associated with the component.</field></row><row><field>Component</field><field>KeyPath</field><field>Y</field><field /><field /><field>File;Registry;ODBCDataSource</field><field>1</field><field>Identifier</field><field /><field>Either the primary key into the File table, Registry table, or ODBCDataSource table. This extract path is stored when the component is installed, and is used to detect the presence of the component and to return the path to it.</field></row><row><field>CustomAction</field><field>Action</field><field>N</field><field /><field /><field /><field /><field>Identifier</field><field /><field>Primary key, name of action, normally appears in sequence table unless private use.</field></row><row><field>CustomAction</field><field>Type</field><field>N</field><field>1</field><field>32767</field><field /><field /><field /><field /><field>The numeric custom action type, consisting of source location, code type, entry, option flags.</field></row><row><field>CustomAction</field><field>Source</field><field>Y</field><field /><field /><field /><field /><field>CustomSource</field><field /><field>The table reference of the source of the code.</field></row><row><field>CustomAction</field><field>Target</field><field>Y</field><field /><field /><field /><field /><field>Formatted</field><field /><field>Excecution parameter, depends on the type of custom action</field></row><row><field>CustomAction</field><field>ExtendedType</field><field>Y</field><field>0</field><field>2147483647</field><field /><field /><field /><field /><field>A numeric custom action type that extends code type or option flags of the Type column.</field></row><row><field>Directory</field><field>Directory</field><field>N</field><field /><field /><field /><field /><field>Identifier</field><field /><field>Unique identifier for directory entry, primary key. If a property by this name is defined, it contains the full path to the directory.</field></row><row><field>Directory</field><field>Directory_Parent</field><field>Y</field><field /><field /><field>Directory</field><field>1</field><field>Identifier</field><field /><field>Reference to the entry in this table specifying the default parent directory. A record parented to itself or with a Null parent represents a root of the install tree.</field></row><row><field>Directory</field><field>DefaultDir</field><field>N</field><field /><field /><field /><field /><field>DefaultDir</field><field /><field>The default sub-path under parent's path.</field></row><row><field>Feature</field><field>Feature</field><field>N</field><field /><field /><field /><field /><field>Identifier</field><field /><field>Primary key used to identify a particular feature record.</field></row><row><field>Feature</field><field>Feature_Parent</field><field>Y</field><field /><field /><field>Feature</field><field>1</field><field>Identifier</field><field /><field>Optional key of a parent record in the same table. If the parent is not selected, then the record will not be installed. Null indicates a root item.</field></row><row><field>Feature</field><field>Title</field><field>Y</field><field /><field /><field /><field /><field>Text</field><field /><field>Short text identifying a visible feature item.</field></row><row><field>Feature</field><field>Description</field><field>Y</field><field /><field /><field /><field /><field>Text</field><field /><field>Longer descriptive text describing a visible feature item.</field></row><row><field>Feature</field><field>Display</field><field>Y</field><field>0</field><field>32767</field><field /><field /><field /><field /><field>Numeric sort order, used to force a specific display ordering.</field></row><row><field>Feature</field><field>Level</field><field>N</field><field>0</field><field>32767</field><field /><field /><field /><field /><field>The install level at which record will be initially selected. An install level of 0 will disable an item and prevent its display.</field></row><row><field>Feature</field><field>Directory_</field><field>Y</field><field /><field /><field>Directory</field><field>1</field><field>UpperCase</field><field /><field>The name of the Directory that can be configured by the UI. A non-null value will enable the browse button.</field></row><row><field>Feature</field><field>Attributes</field><field>N</field><field /><field /><field /><field /><field /><field>0;1;2;4;5;6;8;9;10;16;17;18;20;21;22;24;25;26;32;33;34;36;37;38;48;49;50;52;53;54</field><field>Feature attributes</field></row><row><field>FeatureComponents</field><field>Feature_</field><field>N</field><field /><field /><field>Feature</field><field>1</field><field>Identifier</field><field /><field>Foreign key into Feature table.</field></row><row><field>FeatureComponents</field><field>Component_</field><field>N</field><field /><field /><field>Component</field><field>1</field><field>Identifier</field><field /><field>Foreign key into Component table.</field></row><row><field>File</field><field>File</field><field>N</field><field /><field /><field /><field /><field>Identifier</field><field /><field>Primary key, non-localized token, must match identifier in cabinet.  For uncompressed files, this field is ignored.</field></row><row><field>File</field><field>Component_</field><field>N</field><field /><field /><field>Component</field><field>1</field><field>Identifier</field><field /><field>Foreign key referencing Component that controls the file.</field></row><row><field>File</field><field>FileName</field><field>N</field><field /><field /><field /><field /><field>Filename</field><field /><field>File name used for installation, may be localized.  This may contain a "short name|long name" pair.</field></row><row><field>File</field><field>FileSize</field><field>N</field><field>0</field><field>2147483647</field><field /><field /><field /><field /><field>Size of file in bytes (long integer).</field></row><row><field>File</field><field>Version</field><field>Y</field><field /><field /><field>File</field><field>1</field><field>Version</field><field /><field>Version string for versioned files;  Blank for unversioned files.</field></row><row><field>File</field><field>Language</field><field>Y</field><field /><field /><field /><field /><field>Language</field><field /><field>List of decimal language Ids, comma-separated if more than one.</field></row><row><field>File</field><field>Attributes</field><field>Y</field><field>0</field><field>32767</field><field /><field /><field /><field /><field>Integer containing bit flags representing file attributes (with the decimal value of each bit position in parentheses)</field></row><row><field>File</field><field>Sequence</field><field>N</field><field>1</field><field>2147483647</field><field /><field /><field /><field /><field>Sequence with respect to the media images; order must track cabinet order.</field></row><row><field>InstallExecuteSequence</field><field>Action</field><field>N</field><field /><field /><field /><field /><field>Identifier</field><field /><field>Name of action to invoke, either in the engine or the handler DLL.</field></row><row><field>InstallExecuteSequence</field><field>Condition</field><field>Y</field><field /><field /><field /><field /><field>Condition</field><field /><field>Optional expression which skips the action if evaluates to expFalse.If the expression syntax is invalid, the engine will terminate, returning iesBadActionData.</field></row><row><field>InstallExecuteSequence</field><field>Sequence</field><field>Y</field><field>-4</field><field>32767</field><field /><field /><field /><field /><field>Number that determines the sort order in which the actions are to be executed.  Leave blank to suppress action.</field></row><row><field>InstallUISequence</field><field>Action</field><field>N</field><field /><field /><field /><field /><field>Identifier</field><field /><field>Name of action to invoke, either in the engine or the handler DLL.</field></row><row><field>InstallUISequence</field><field>Condition</field><field>Y</field><field /><field /><field /><field /><field>Condition</field><field /><field>Optional expression which skips the action if evaluates to expFalse.If the expression syntax is invalid, the engine will terminate, returning iesBadActionData.</field></row><row><field>InstallUISequence</field><field>Sequence</field><field>Y</field><field>-4</field><field>32767</field><field /><field /><field /><field /><field>Number that determines the sort order in which the actions are to be executed.  Leave blank to suppress action.</field></row><row><field>LaunchCondition</field><field>Condition</field><field>N</field><field /><field /><field /><field /><field>Condition</field><field /><field>Expression which must evaluate to TRUE in order for install to commence.</field></row><row><field>LaunchCondition</field><field>Description</field><field>N</field><field /><field /><field /><field /><field>Formatted</field><field /><field>Localizable text to display when condition fails and install must abort.</field></row><row><field>Media</field><field>DiskId</field><field>N</field><field>1</field><field>32767</field><field /><field /><field /><field /><field>Primary key, integer to determine sort order for table.</field></row><row><field>Media</field><field>LastSequence</field><field>N</field><field>0</field><field>2147483647</field><field /><field /><field /><field /><field>File sequence number for the last file for this media.</field></row><row><field>Media</field><field>DiskPrompt</field><field>Y</field><field /><field /><field /><field /><field>Text</field><field /><field>Disk name: the visible text actually printed on the disk.  This will be used to prompt the user when this disk needs to be inserted.</field></row><row><field>Media</field><field>Cabinet</field><field>Y</field><field /><field /><field /><field /><field>Cabinet</field><field /><field>If some or all of the files stored on the media are compressed in a cabinet, the name of that cabinet.</field></row><row><field>Media</field><field>VolumeLabel</field><field>Y</field><field /><field /><field /><field /><field>Text</field><field /><field>The label attributed to the volume.</field></row><row><field>Media</field><field>Source</field><field>Y</field><field /><field /><field /><field /><field>Property</field><field /><field>The property defining the location of the cabinet file.</field></row><row><field>MsiFileHash</field><field>File_</field><field>N</field><field /><field /><field>File</field><field>1</field><field>Identifier</field><field /><field>Primary key, foreign key into File table referencing file with this hash</field></row><row><field>MsiFileHash</field><field>Options</field><field>N</field><field>0</field><field>32767</field><field /><field /><field /><field /><field>Various options and attributes for this hash.</field></row><row><field>MsiFileHash</field><field>HashPart1</field><field>N</field><field /><field /><field /><field /><field /><field /><field>Size of file in bytes (long integer).</field></row><row><field>MsiFileHash</field><field>HashPart2</field><field>N</field><field /><field /><field /><field /><field /><field /><field>Size of file in bytes (long integer).</field></row><row><field>MsiFileHash</field><field>HashPart3</field><field>N</field><field /><field /><field /><field /><field /><field /><field>Size of file in bytes (long integer).</field></row><row><field>MsiFileHash</field><field>HashPart4</field><field>N</field><field /><field /><field /><field /><field /><field /><field>Size of file in bytes (long integer).</field></row><row><field>Property</field><field>Property</field><field>N</field><field /><field /><field /><field /><field>Identifier</field><field /><field>Name of property, uppercase if settable by launcher or loader.</field></row><row><field>Property</field><field>Value</field><field>N</field><field /><field /><field /><field /><field>Text</field><field /><field>String value for property.  Never null or empty.</field></row><row><field>ServiceControl</field><field>ServiceControl</field><field>N</field><field /><field /><field /><field /><field>Identifier</field><field /><field>Primary key, non-localized token.</field></row><row><field>ServiceControl</field><field>Name</field><field>N</field><field /><field /><field /><field /><field>Formatted</field><field /><field>Name of a service. /, \, comma and space are invalid</field></row><row><field>ServiceControl</field><field>Event</field><field>N</field><field>0</field><field>187</field><field /><field /><field /><field /><field>Bit field:  Install:  0x1 = Start, 0x2 = Stop, 0x8 = Delete, Uninstall: 0x10 = Start, 0x20 = Stop, 0x80 = Delete</field></row><row><field>ServiceControl</field><field>Arguments</field><field>Y</field><field /><field /><field /><field /><field>Formatted</field><field /><field>Arguments for the service.  Separate by [~].</field></row><row><field>ServiceControl</field><field>Wait</field><field>Y</field><field>0</field><field>1</field><field /><field /><field /><field /><field>Boolean for whether to wait for the service to fully start</field></row><row><field>ServiceControl</field><field>Component_</field><field>N</field><field /><field /><field>Component</field><field>1</field><field>Identifier</field><field /><field>Required foreign key into the Component Table that controls the startup of the service</field></row><row><field>ServiceInstall</field><field>ServiceInstall</field><field>N</field><field /><field /><field /><field /><field>Identifier</field><field /><field>Primary key, non-localized token.</field></row><row><field>ServiceInstall</field><field>Name</field><field>N</field><field /><field /><field /><field /><field>Formatted</field><field /><field>Internal Name of the Service</field></row><row><field>ServiceInstall</field><field>DisplayName</field><field>Y</field><field /><field /><field /><field /><field>Formatted</field><field /><field>External Name of the Service</field></row><row><field>ServiceInstall</field><field>ServiceType</field><field>N</field><field>-2147483647</field><field>2147483647</field><field /><field /><field /><field /><field>Type of the service</field></row><row><field>ServiceInstall</field><field>StartType</field><field>N</field><field>0</field><field>4</field><field /><field /><field /><field /><field>Type of the service</field></row><row><field>ServiceInstall</field><field>ErrorControl</field><field>N</field><field>-2147483647</field><field>2147483647</field><field /><field /><field /><field /><field>Severity of error if service fails to start</field></row><row><field>ServiceInstall</field><field>LoadOrderGroup</field><field>Y</field><field /><field /><field /><field /><field>Formatted</field><field /><field>LoadOrderGroup</field></row><row><field>ServiceInstall</field><field>Dependencies</field><field>Y</field><field /><field /><field /><field /><field>Formatted</field><field /><field>Other services this depends on to start.  Separate by [~], and end with [~][~]</field></row><row><field>ServiceInstall</field><field>StartName</field><field>Y</field><field /><field /><field /><field /><field>Formatted</field><field /><field>User or object name to run service as</field></row><row><field>ServiceInstall</field><field>Password</field><field>Y</field><field /><field /><field /><field /><field>Formatted</field><field /><field>password to run service with.  (with StartName)</field></row><row><field>ServiceInstall</field><field>Arguments</field><field>Y</field><field /><field /><field /><field /><field>Formatted</field><field /><field>Arguments to include in every start of the service, passed to WinMain</field></row><row><field>ServiceInstall</field><field>Component_</field><field>N</field><field /><field /><field>Component</field><field>1</field><field>Identifier</field><field /><field>Required foreign key into the Component Table that controls the startup of the service</field></row><row><field>ServiceInstall</field><field>Description</field><field>Y</field><field /><field /><field /><field /><field>Text</field><field /><field>Description of service.</field></row><row><field>Upgrade</field><field>UpgradeCode</field><field>N</field><field /><field /><field /><field /><field>Guid</field><field /><field>The UpgradeCode GUID belonging to the products in this set.</field></row><row><field>Upgrade</field><field>VersionMin</field><field>Y</field><field /><field /><field /><field /><field>Text</field><field /><field>The minimum ProductVersion of the products in this set.  The set may or may not include products with this particular version.</field></row><row><field>Upgrade</field><field>VersionMax</field><field>Y</field><field /><field /><field /><field /><field>Text</field><field /><field>The maximum ProductVersion of the products in this set.  The set may or may not include products with this particular version.</field></row><row><field>Upgrade</field><field>Language</field><field>Y</field><field /><field /><field /><field /><field>Language</field><field /><field>A comma-separated list of languages for either products in this set or products not in this set.</field></row><row><field>Upgrade</field><field>Attributes</field><field>N</field><field>0</field><field>2147483647</field><field /><field /><field /><field /><field>The attributes of this product set.</field></row><row><field>Upgrade</field><field>Remove</field><field>Y</field><field /><field /><field /><field /><field>Formatted</field><field /><field>The list of features to remove when uninstalling a product from this set.  The default is "ALL".</field></row><row><field>Upgrade</field><field>ActionProperty</field><field>N</field><field /><field /><field /><field /><field>UpperCase</field><field /><field>The property to set when a product in this set is found.</field></row></table><table name="AdminExecuteSequence" xmlns="http://schemas.microsoft.com/wix/2006/objects"><row><field>CostInitialize</field><field /><field>800</field></row><row><field>FileCost</field><field /><field>900</field></row><row><field>CostFinalize</field><field /><field>1000</field></row><row><field>InstallValidate</field><field /><field>1400</field></row><row><field>InstallInitialize</field><field /><field>1500</field></row><row><field>InstallAdminPackage</field><field /><field>3900</field></row><row><field>InstallFiles</field><field /><field>4000</field></row><row><field>InstallFinalize</field><field /><field>6600</field></row></table><table name="AdminUISequence" xmlns="http://schemas.microsoft.com/wix/2006/objects"><row><field>CostInitialize</field><field /><field>800</field></row><row><field>FileCost</field><field /><field>900</field></row><row><field>CostFinalize</field><field /><field>1000</field></row><row><field>ExecuteAction</field><field /><field>1300</field></row></table><table name="AdvtExecuteSequence" xmlns="http://schemas.microsoft.com/wix/2006/objects"><row><field>CostInitialize</field><field /><field>800</field></row><row><field>CostFinalize</field><field /><field>1000</field></row><row><field>InstallValidate</field><field /><field>1400</field></row><row><field>InstallInitialize</field><field /><field>1500</field></row><row><field>PublishFeatures</field><field /><field>6300</field></row><row><field>PublishProduct</field><field /><field>6400</field></row><row><field>InstallFinalize</field><field /><field>6600</field></row></table><table name="Component" xmlns="http://schemas.microsoft.com/wix/2006/objects"><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*64"><field>ServiceExe</field><field>{4DD20919-9DCB-4405-88A7-A5A9F57972CD}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>ServiceExe</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*87"><field>ServiceDll</field><field>{5EE20919-9DCB-4405-88A7-A5A9F57972CD}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>ServiceDll</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*92"><field>AppSettings</field><field>{6FF20919-9DCB-4405-88A7-A5A9F57972CD}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>AppSettings</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*96"><field>RuntimeConfig</field><field>{7FF20919-9DCB-4405-88A7-A5A9F57972CD}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>RuntimeConfig</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*100"><field>DepsJson</field><field>{8EE20919-9DCB-4405-88A7-A5A9F57972CD}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>DepsJson</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*105"><field>MicrosoftExtensionsHostingAbstractions</field><field>{9DD20919-9DCB-4405-88A7-A5A9F57972CD}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>MicrosoftExtensionsHostingAbstractions</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*109"><field>MicrosoftExtensionsHosting</field><field>{0CC20919-9DCB-4405-88A7-A5A9F57972CD}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>MicrosoftExtensionsHosting</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*113"><field>MicrosoftExtensionsDI</field><field>{1BB20919-9DCB-4405-88A7-A5A9F57972CD}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>MicrosoftExtensionsDI</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*117"><field>MicrosoftExtensionsDIAbstractions</field><field>{2AA20919-9DCB-4405-88A7-A5A9F57972CD}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>MicrosoftExtensionsDIAbstractions</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*121"><field>MicrosoftExtensionsLogging</field><field>{3FF30919-9DCB-4405-88A7-A5A9F57972CD}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>MicrosoftExtensionsLogging</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*125"><field>MicrosoftExtensionsLoggingAbstractions</field><field>{4EE30919-9DCB-4405-88A7-A5A9F57972CD}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>MicrosoftExtensionsLoggingAbstractions</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*129"><field>MicrosoftExtensionsLoggingConsole</field><field>{4DD30919-9DCB-4405-88A7-A5A9F57972CD}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>MicrosoftExtensionsLoggingConsole</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*133"><field>MicrosoftExtensionsLoggingConfiguration</field><field>{4CC30919-9DCB-4405-88A7-A5A9F57972CD}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>MicrosoftExtensionsLoggingConfiguration</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*137"><field>MicrosoftExtensionsLoggingEventLog</field><field>{4BB30919-9DCB-4405-88A7-A5A9F57972CD}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>MicrosoftExtensionsLoggingEventLog</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*141"><field>MicrosoftExtensionsLoggingEventSource</field><field>{4AA30919-9DCB-4405-88A7-A5A9F57972CD}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>MicrosoftExtensionsLoggingEventSource</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*145"><field>MicrosoftExtensionsLoggingDebug</field><field>{4FF30819-9DCB-4405-88A7-A5A9F57972CD}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>MicrosoftExtensionsLoggingDebug</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*149"><field>MicrosoftExtensionsConfiguration</field><field>{5DD30919-9DCB-4405-88A7-A5A9F57972CD}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>MicrosoftExtensionsConfiguration</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*153"><field>MicrosoftExtensionsConfigurationAbstractions</field><field>{6CC30919-9DCB-4405-88A7-A5A9F57972CD}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>MicrosoftExtensionsConfigurationAbstractions</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*157"><field>MicrosoftExtensionsConfigurationJson</field><field>{7BB30919-9DCB-4405-88A7-A5A9F57972CD}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>MicrosoftExtensionsConfigurationJson</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*161"><field>MicrosoftExtensionsConfigurationFileExtensions</field><field>{8AA30919-9DCB-4405-88A7-A5A9F57972CD}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>MicrosoftExtensionsConfigurationFileExtensions</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*165"><field>MicrosoftExtensionsConfigurationEnvironmentVariables</field><field>{9FF40919-9DCB-4405-88A7-A5A9F57972CD}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>MicrosoftExtensionsConfigurationEnvironmentVariables</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*169"><field>MicrosoftExtensionsConfigurationCommandLine</field><field>{0EE40919-9DCB-4405-88A7-A5A9F57972CD}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>MicrosoftExtensionsConfigurationCommandLine</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*173"><field>MicrosoftExtensionsConfigurationBinder</field><field>{1DD40919-9DCB-4405-88A7-A5A9F57972CD}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>MicrosoftExtensionsConfigurationBinder</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*177"><field>MicrosoftExtensionsOptions</field><field>{2CC40919-9DCB-4405-88A7-A5A9F57972CD}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>MicrosoftExtensionsOptions</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*181"><field>MicrosoftExtensionsOptionsConfigurationExtensions</field><field>{3BB40919-9DCB-4405-88A7-A5A9F57972CD}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>MicrosoftExtensionsOptionsConfigurationExtensions</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*185"><field>MicrosoftExtensionsPrimitives</field><field>{4AA40919-9DCB-4405-88A7-A5A9F57972CD}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>MicrosoftExtensionsPrimitives</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*189"><field>MicrosoftExtensionsFileProviders</field><field>{5FF50919-9DCB-4405-88A7-A5A9F57972CD}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>MicrosoftExtensionsFileProviders</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*193"><field>MicrosoftExtensionsFileProvidersPhysical</field><field>{6EE50919-9DCB-4405-88A7-A5A9F57972CD}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>MicrosoftExtensionsFileProvidersPhysical</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*197"><field>MicrosoftExtensionsFileSystemGlobbing</field><field>{7DD50919-9DCB-4405-88A7-A5A9F57972CD}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>MicrosoftExtensionsFileSystemGlobbing</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*202"><field>MicrosoftExtensionsHostingWindowsServices</field><field>{8CC50919-9DCB-4405-88A7-A5A9F57972CD}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>MicrosoftExtensionsHostingWindowsServices</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*207"><field>SystemDiagnosticsEventLog</field><field>{9BB50919-9DCB-4405-88A7-A5A9F57972CD}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>SystemDiagnosticsEventLog</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*211"><field>SystemServiceProcessServiceController</field><field>{0AA50919-9DCB-4405-88A7-A5A9F57972CD}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>SystemServiceProcessServiceController</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*215"><field>SystemTextJson</field><field>{1FF60919-9DCB-4405-88A7-A5A9F57972CD}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>SystemTextJson</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*219"><field>YamlDotNet</field><field>{4CC60919-9DCB-4405-88A7-A5A9F57972CD}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>YamlDotNet</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*224"><field>AppSettingsDevelopment</field><field>{8EE70919-9DCB-4405-88A7-A5A9F57972CD}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>AppSettingsDevelopment</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*229"><field>StartServiceScript</field><field>{9EE70919-9DCB-4405-88A7-A5A9F57972CD}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>StartServiceBat</field></row></table><table name="CustomAction" xmlns="http://schemas.microsoft.com/wix/2006/objects"><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*35"><field>SetServiceTimeout</field><field>3106</field><field>INSTALLFOLDER</field><field>reg add "HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control" /v "ServicesPipeTimeout" /t REG_DWORD /d "180000" /f</field><field /></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*41"><field>SetServiceConfig</field><field>3106</field><field>INSTALLFOLDER</field><field>sc config LSBTelemetryService start= auto obj= LocalSystem</field><field /></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*47"><field>SetServiceFailureActions</field><field>3106</field><field>INSTALLFOLDER</field><field>sc failure LSBTelemetryService reset= 86400 actions= restart/30000/restart/60000/restart/60000</field><field /></row></table><table name="Directory" xmlns="http://schemas.microsoft.com/wix/2006/objects"><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*25"><field>INSTALLFOLDER</field><field>CompanyFolder</field><field>2u63yf5o|TelemetryService</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*24"><field>CompanyFolder</field><field>ProgramFiles64Folder</field><field>LSB</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*23"><field>ProgramFiles64Folder</field><field>TARGETDIR</field><field>.</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*22"><field>TARGETDIR</field><field /><field>SourceDir</field></row></table><table name="Feature" xmlns="http://schemas.microsoft.com/wix/2006/objects"><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*30"><field>Complete</field><field /><field /><field /><field>2</field><field>1</field><field /><field>0</field></row></table><table name="FeatureComponents" xmlns="http://schemas.microsoft.com/wix/2006/objects"><row sectionId="wix.section.8"><field>Complete</field><field>AppSettings</field></row><row sectionId="wix.section.8"><field>Complete</field><field>AppSettingsDevelopment</field></row><row sectionId="wix.section.8"><field>Complete</field><field>DepsJson</field></row><row sectionId="wix.section.8"><field>Complete</field><field>MicrosoftExtensionsConfiguration</field></row><row sectionId="wix.section.8"><field>Complete</field><field>MicrosoftExtensionsConfigurationAbstractions</field></row><row sectionId="wix.section.8"><field>Complete</field><field>MicrosoftExtensionsConfigurationBinder</field></row><row sectionId="wix.section.8"><field>Complete</field><field>MicrosoftExtensionsConfigurationCommandLine</field></row><row sectionId="wix.section.8"><field>Complete</field><field>MicrosoftExtensionsConfigurationEnvironmentVariables</field></row><row sectionId="wix.section.8"><field>Complete</field><field>MicrosoftExtensionsConfigurationFileExtensions</field></row><row sectionId="wix.section.8"><field>Complete</field><field>MicrosoftExtensionsConfigurationJson</field></row><row sectionId="wix.section.8"><field>Complete</field><field>MicrosoftExtensionsDI</field></row><row sectionId="wix.section.8"><field>Complete</field><field>MicrosoftExtensionsDIAbstractions</field></row><row sectionId="wix.section.8"><field>Complete</field><field>MicrosoftExtensionsFileProviders</field></row><row sectionId="wix.section.8"><field>Complete</field><field>MicrosoftExtensionsFileProvidersPhysical</field></row><row sectionId="wix.section.8"><field>Complete</field><field>MicrosoftExtensionsFileSystemGlobbing</field></row><row sectionId="wix.section.8"><field>Complete</field><field>MicrosoftExtensionsHosting</field></row><row sectionId="wix.section.8"><field>Complete</field><field>MicrosoftExtensionsHostingAbstractions</field></row><row sectionId="wix.section.8"><field>Complete</field><field>MicrosoftExtensionsHostingWindowsServices</field></row><row sectionId="wix.section.8"><field>Complete</field><field>MicrosoftExtensionsLogging</field></row><row sectionId="wix.section.8"><field>Complete</field><field>MicrosoftExtensionsLoggingAbstractions</field></row><row sectionId="wix.section.8"><field>Complete</field><field>MicrosoftExtensionsLoggingConfiguration</field></row><row sectionId="wix.section.8"><field>Complete</field><field>MicrosoftExtensionsLoggingConsole</field></row><row sectionId="wix.section.8"><field>Complete</field><field>MicrosoftExtensionsLoggingDebug</field></row><row sectionId="wix.section.8"><field>Complete</field><field>MicrosoftExtensionsLoggingEventLog</field></row><row sectionId="wix.section.8"><field>Complete</field><field>MicrosoftExtensionsLoggingEventSource</field></row><row sectionId="wix.section.8"><field>Complete</field><field>MicrosoftExtensionsOptions</field></row><row sectionId="wix.section.8"><field>Complete</field><field>MicrosoftExtensionsOptionsConfigurationExtensions</field></row><row sectionId="wix.section.8"><field>Complete</field><field>MicrosoftExtensionsPrimitives</field></row><row sectionId="wix.section.8"><field>Complete</field><field>RuntimeConfig</field></row><row sectionId="wix.section.8"><field>Complete</field><field>ServiceDll</field></row><row sectionId="wix.section.8"><field>Complete</field><field>ServiceExe</field></row><row sectionId="wix.section.8"><field>Complete</field><field>StartServiceScript</field></row><row sectionId="wix.section.8"><field>Complete</field><field>SystemDiagnosticsEventLog</field></row><row sectionId="wix.section.8"><field>Complete</field><field>SystemServiceProcessServiceController</field></row><row sectionId="wix.section.8"><field>Complete</field><field>SystemTextJson</field></row><row sectionId="wix.section.8"><field>Complete</field><field>YamlDotNet</field></row></table><table name="File" xmlns="http://schemas.microsoft.com/wix/2006/objects"><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*65"><field>ServiceExe</field><field>ServiceExe</field><field>_1h8sy4e.exe|LSB.LogMonitor.Service.exe</field><field>151040</field><field>*******</field><field>0</field><field>512</field><field>31</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*88"><field>ServiceDll</field><field>ServiceDll</field><field>jrdrooxf.dll|LSB.LogMonitor.Service.dll</field><field>132096</field><field>*******</field><field>0</field><field>512</field><field>30</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*93"><field>AppSettings</field><field>AppSettings</field><field>kfc7hi9q.jso|appsettings.json</field><field>746</field><field /><field /><field>512</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*97"><field>RuntimeConfig</field><field>RuntimeConfig</field><field>2u9wsq2q.jso|LSB.LogMonitor.Service.runtimeconfig.json</field><field>285</field><field /><field /><field>512</field><field>29</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*101"><field>DepsJson</field><field>DepsJson</field><field>or5xttlf.jso|LSB.LogMonitor.Service.deps.json</field><field>69326</field><field /><field /><field>512</field><field>3</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*106"><field>MicrosoftExtensionsHostingAbstractions</field><field>MicrosoftExtensionsHostingAbstractions</field><field>vxsgzw4e.dll|Microsoft.Extensions.Hosting.Abstractions.dll</field><field>27776</field><field>6.0.21.52210</field><field>0</field><field>512</field><field>17</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*110"><field>MicrosoftExtensionsHosting</field><field>MicrosoftExtensionsHosting</field><field>g1t4n4bi.dll|Microsoft.Extensions.Hosting.dll</field><field>55400</field><field>6.0.222.6406</field><field>0</field><field>512</field><field>16</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*114"><field>MicrosoftExtensionsDI</field><field>MicrosoftExtensionsDI</field><field>rv0yalpk.dll|Microsoft.Extensions.DependencyInjection.dll</field><field>81536</field><field>6.0.21.52210</field><field>0</field><field>512</field><field>11</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*118"><field>MicrosoftExtensionsDIAbstractions</field><field>MicrosoftExtensionsDIAbstractions</field><field>qcgdkoth.dll|Microsoft.Extensions.DependencyInjection.Abstractions.dll</field><field>43632</field><field>6.0.21.52210</field><field>0</field><field>512</field><field>12</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*122"><field>MicrosoftExtensionsLogging</field><field>MicrosoftExtensionsLogging</field><field>1ybxot9n.dll|Microsoft.Extensions.Logging.dll</field><field>44656</field><field>6.0.21.52210</field><field>0</field><field>512</field><field>19</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*126"><field>MicrosoftExtensionsLoggingAbstractions</field><field>MicrosoftExtensionsLoggingAbstractions</field><field>ag0us1fy.dll|Microsoft.Extensions.Logging.Abstractions.dll</field><field>62064</field><field>6.0.21.52210</field><field>0</field><field>512</field><field>20</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*130"><field>MicrosoftExtensionsLoggingConsole</field><field>MicrosoftExtensionsLoggingConsole</field><field>hbb5wzhh.dll|Microsoft.Extensions.Logging.Console.dll</field><field>50304</field><field>6.0.21.52210</field><field>0</field><field>512</field><field>22</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*134"><field>MicrosoftExtensionsLoggingConfiguration</field><field>MicrosoftExtensionsLoggingConfiguration</field><field>il-0lzlt.dll|Microsoft.Extensions.Logging.Configuration.dll</field><field>26736</field><field>6.0.21.52210</field><field>0</field><field>512</field><field>21</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*138"><field>MicrosoftExtensionsLoggingEventLog</field><field>MicrosoftExtensionsLoggingEventLog</field><field>vromxrgw.dll|Microsoft.Extensions.Logging.EventLog.dll</field><field>24192</field><field>6.0.21.52210</field><field>0</field><field>512</field><field>24</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*142"><field>MicrosoftExtensionsLoggingEventSource</field><field>MicrosoftExtensionsLoggingEventSource</field><field>toeg88yj.dll|Microsoft.Extensions.Logging.EventSource.dll</field><field>32896</field><field>6.0.21.52210</field><field>0</field><field>512</field><field>25</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*146"><field>MicrosoftExtensionsLoggingDebug</field><field>MicrosoftExtensionsLoggingDebug</field><field>c9v78xmv.dll|Microsoft.Extensions.Logging.Debug.dll</field><field>18048</field><field>6.0.21.52210</field><field>0</field><field>512</field><field>23</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*150"><field>MicrosoftExtensionsConfiguration</field><field>MicrosoftExtensionsConfiguration</field><field>v73g6yvi.dll|Microsoft.Extensions.Configuration.dll</field><field>36464</field><field>6.0.21.52210</field><field>0</field><field>512</field><field>4</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*154"><field>MicrosoftExtensionsConfigurationAbstractions</field><field>MicrosoftExtensionsConfigurationAbstractions</field><field>z3yq_gql.dll|Microsoft.Extensions.Configuration.Abstractions.dll</field><field>25216</field><field>6.0.21.52210</field><field>0</field><field>512</field><field>5</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*158"><field>MicrosoftExtensionsConfigurationJson</field><field>MicrosoftExtensionsConfigurationJson</field><field>uioj3jgo.dll|Microsoft.Extensions.Configuration.Json.dll</field><field>25728</field><field>6.0.21.52210</field><field>0</field><field>512</field><field>10</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*162"><field>MicrosoftExtensionsConfigurationFileExtensions</field><field>MicrosoftExtensionsConfigurationFileExtensions</field><field>awalw0wb.dll|Microsoft.Extensions.Configuration.FileExtensions.dll</field><field>26224</field><field>6.0.21.52210</field><field>0</field><field>512</field><field>9</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*166"><field>MicrosoftExtensionsConfigurationEnvironmentVariables</field><field>MicrosoftExtensionsConfigurationEnvironmentVariables</field><field>mp1dyo44.dll|Microsoft.Extensions.Configuration.EnvironmentVariables.dll</field><field>18536</field><field>6.0.222.6406</field><field>0</field><field>512</field><field>8</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*170"><field>MicrosoftExtensionsConfigurationCommandLine</field><field>MicrosoftExtensionsConfigurationCommandLine</field><field>7qrr8c_n.dll|Microsoft.Extensions.Configuration.CommandLine.dll</field><field>23152</field><field>6.0.21.52210</field><field>0</field><field>512</field><field>7</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*174"><field>MicrosoftExtensionsConfigurationBinder</field><field>MicrosoftExtensionsConfigurationBinder</field><field>diog2odo.dll|Microsoft.Extensions.Configuration.Binder.dll</field><field>33920</field><field>6.0.21.52210</field><field>0</field><field>512</field><field>6</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*178"><field>MicrosoftExtensionsOptions</field><field>MicrosoftExtensionsOptions</field><field>liciwkcm.dll|Microsoft.Extensions.Options.dll</field><field>59008</field><field>6.0.21.52210</field><field>0</field><field>512</field><field>26</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*182"><field>MicrosoftExtensionsOptionsConfigurationExtensions</field><field>MicrosoftExtensionsOptionsConfigurationExtensions</field><field>6k3isirh.dll|Microsoft.Extensions.Options.ConfigurationExtensions.dll</field><field>22656</field><field>6.0.21.52210</field><field>0</field><field>512</field><field>27</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*186"><field>MicrosoftExtensionsPrimitives</field><field>MicrosoftExtensionsPrimitives</field><field>7b1w1ul_.dll|Microsoft.Extensions.Primitives.dll</field><field>40048</field><field>6.0.21.52210</field><field>0</field><field>512</field><field>28</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*190"><field>MicrosoftExtensionsFileProviders</field><field>MicrosoftExtensionsFileProviders</field><field>vxhm25h7.dll|Microsoft.Extensions.FileProviders.Abstractions.dll</field><field>21120</field><field>6.0.21.52210</field><field>0</field><field>512</field><field>13</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*194"><field>MicrosoftExtensionsFileProvidersPhysical</field><field>MicrosoftExtensionsFileProvidersPhysical</field><field>vabwwy5a.dll|Microsoft.Extensions.FileProviders.Physical.dll</field><field>42624</field><field>6.0.21.52210</field><field>0</field><field>512</field><field>14</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*198"><field>MicrosoftExtensionsFileSystemGlobbing</field><field>MicrosoftExtensionsFileSystemGlobbing</field><field>tgjhxkow.dll|Microsoft.Extensions.FileSystemGlobbing.dll</field><field>44160</field><field>6.0.21.52210</field><field>0</field><field>512</field><field>15</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*203"><field>MicrosoftExtensionsHostingWindowsServices</field><field>MicrosoftExtensionsHostingWindowsServices</field><field>8bnwjk5y.dll|Microsoft.Extensions.Hosting.WindowsServices.dll</field><field>25216</field><field>6.0.1823.26907</field><field>0</field><field>512</field><field>18</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*208"><field>SystemDiagnosticsEventLog</field><field>SystemDiagnosticsEventLog</field><field>c6p34-y7.dll|System.Diagnostics.EventLog.dll</field><field>132200</field><field>6.0.21.52210</field><field>0</field><field>512</field><field>33</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*212"><field>SystemServiceProcessServiceController</field><field>SystemServiceProcessServiceController</field><field>5qxquscr.dll|System.ServiceProcess.ServiceController.dll</field><field>63152</field><field>6.0.1823.26907</field><field>0</field><field>512</field><field>34</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*216"><field>SystemTextJson</field><field>SystemTextJson</field><field>1datdby4.dll|System.Text.Json.dll</field><field>1486112</field><field>6.0.3624.51421</field><field>0</field><field>512</field><field>35</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*220"><field>YamlDotNet</field><field>YamlDotNet</field><field>okdhriov.dll|YamlDotNet.dll</field><field>270336</field><field>13.7.1.0</field><field>0</field><field>512</field><field>36</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*225"><field>AppSettingsDevelopment</field><field>AppSettingsDevelopment</field><field>wnub-aor.jso|appsettings.Development.json</field><field>682</field><field /><field /><field>512</field><field>2</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*230"><field>StartServiceBat</field><field>StartServiceScript</field><field>u7l6bkyh.bat|start_service.bat</field><field>539</field><field /><field /><field>512</field><field>32</field></row></table><table name="InstallExecuteSequence" xmlns="http://schemas.microsoft.com/wix/2006/objects"><row><field>FindRelatedProducts</field><field /><field>25</field></row><row><field>LaunchConditions</field><field /><field>100</field></row><row><field>ValidateProductID</field><field /><field>700</field></row><row><field>CostInitialize</field><field /><field>800</field></row><row><field>FileCost</field><field /><field>900</field></row><row><field>CostFinalize</field><field /><field>1000</field></row><row><field>MigrateFeatureStates</field><field /><field>1200</field></row><row><field>InstallValidate</field><field /><field>1400</field></row><row><field>InstallInitialize</field><field /><field>1500</field></row><row><field>ProcessComponents</field><field /><field>1600</field></row><row><field>UnpublishFeatures</field><field /><field>1800</field></row><row><field>StopServices</field><field>VersionNT</field><field>1900</field></row><row><field>DeleteServices</field><field>VersionNT</field><field>2000</field></row><row><field>RemoveFiles</field><field /><field>3500</field></row><row><field>InstallFiles</field><field /><field>4000</field></row><row><field>InstallServices</field><field>VersionNT</field><field>5800</field></row><row><field>StartServices</field><field>VersionNT</field><field>5900</field></row><row><field>RegisterUser</field><field /><field>6000</field></row><row><field>RegisterProduct</field><field /><field>6100</field></row><row><field>PublishFeatures</field><field /><field>6300</field></row><row><field>PublishProduct</field><field /><field>6400</field></row><row><field>InstallFinalize</field><field /><field>6600</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*19"><field>RemoveExistingProducts</field><field /><field>1501</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*54"><field>SetServiceTimeout</field><field>NOT Installed</field><field>5801</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*55"><field>SetServiceConfig</field><field>NOT Installed</field><field>5802</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*56"><field>SetServiceFailureActions</field><field>NOT Installed</field><field>5803</field></row></table><table name="InstallUISequence" xmlns="http://schemas.microsoft.com/wix/2006/objects"><row><field>FindRelatedProducts</field><field /><field>25</field></row><row><field>LaunchConditions</field><field /><field>100</field></row><row><field>ValidateProductID</field><field /><field>700</field></row><row><field>CostInitialize</field><field /><field>800</field></row><row><field>FileCost</field><field /><field>900</field></row><row><field>CostFinalize</field><field /><field>1000</field></row><row><field>MigrateFeatureStates</field><field /><field>1200</field></row><row><field>ExecuteAction</field><field /><field>1300</field></row></table><table name="LaunchCondition" xmlns="http://schemas.microsoft.com/wix/2006/objects"><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*19"><field>NOT WIX_DOWNGRADE_DETECTED</field><field>A newer version is already installed.</field></row></table><table name="Media" xmlns="http://schemas.microsoft.com/wix/2006/objects"><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*16"><field>1</field><field>36</field><field /><field>#files.cab</field><field /><field /></row></table><table name="MsiFileHash" xmlns="http://schemas.microsoft.com/wix/2006/objects"><row sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*93"><field>AppSettings</field><field>0</field><field>2105582580</field><field>1119749595</field><field>-1120280015</field><field>1315624272</field></row><row sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*97"><field>RuntimeConfig</field><field>0</field><field>2034833108</field><field>-1204502971</field><field>-1227169010</field><field>1550556006</field></row><row sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*101"><field>DepsJson</field><field>0</field><field>1997920258</field><field>102744212</field><field>-89808902</field><field>260112858</field></row><row sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*225"><field>AppSettingsDevelopment</field><field>0</field><field>481611319</field><field>-46899094</field><field>-1546446747</field><field>955884930</field></row><row sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*230"><field>StartServiceBat</field><field>0</field><field>-1451190610</field><field>1577725709</field><field>-1427278389</field><field>1557151232</field></row></table><table name="Property" xmlns="http://schemas.microsoft.com/wix/2006/objects"><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*12"><field>ALLUSERS</field><field>1</field></row><row sectionId="*.Manufacturer" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*5"><field>Manufacturer</field><field>LSB</field></row><row sectionId="*.ProductCode" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*5"><field>ProductCode</field><field>{A3B91DC8-335C-4395-8B00-25E02C479FF1}</field></row><row sectionId="*.ProductLanguage" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*5"><field>ProductLanguage</field><field>1033</field></row><row sectionId="*.ProductName" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*5"><field>ProductName</field><field>LSB Telemetry Service</field></row><row sectionId="*.ProductVersion" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*5"><field>ProductVersion</field><field>*******</field></row><row sectionId="*.UpgradeCode" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*5"><field>UpgradeCode</field><field>{EB8A2174-7977-45CB-8EDB-936FBC25378E}</field></row><row><field>SecureCustomProperties</field><field>WIX_DOWNGRADE_DETECTED;WIX_UPGRADE_DETECTED</field></row></table><table name="ServiceControl" xmlns="http://schemas.microsoft.com/wix/2006/objects"><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*79"><field>ServiceControl</field><field>LSBTelemetryService</field><field>162</field><field /><field>1</field><field>ServiceExe</field></row></table><table name="ServiceInstall" xmlns="http://schemas.microsoft.com/wix/2006/objects"><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*70"><field>InstallService</field><field>LSBTelemetryService</field><field>LSB Telemetry Service</field><field>16</field><field>2</field><field>1</field><field /><field /><field>LocalSystem</field><field /><field /><field>ServiceExe</field><field>LSB Telemetry Service - System monitoring</field></row></table><table name="Upgrade" xmlns="http://schemas.microsoft.com/wix/2006/objects"><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*19"><field>{EB8A2174-7977-45CB-8EDB-936FBC25378E}</field><field /><field>*******</field><field /><field>1</field><field /><field>WIX_UPGRADE_DETECTED</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*19"><field>{EB8A2174-7977-45CB-8EDB-936FBC25378E}</field><field>*******</field><field /><field /><field>2</field><field /><field>WIX_DOWNGRADE_DETECTED</field></row></table><table name="WixAction" xmlns="http://schemas.microsoft.com/wix/2006/objects"><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*19"><field>InstallExecuteSequence</field><field>RemoveExistingProducts</field><field /><field>1501</field><field /><field>InstallInitialize</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*54"><field>InstallExecuteSequence</field><field>SetServiceTimeout</field><field>NOT Installed</field><field>5801</field><field /><field>InstallServices</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*55"><field>InstallExecuteSequence</field><field>SetServiceConfig</field><field>NOT Installed</field><field>5802</field><field /><field>SetServiceTimeout</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*56"><field>InstallExecuteSequence</field><field>SetServiceFailureActions</field><field>NOT Installed</field><field>5803</field><field /><field>SetServiceConfig</field><field>0</field></row></table><table name="WixBuildInfo" xmlns="http://schemas.microsoft.com/wix/2006/objects"><row><field>3.14.1.8722</field><field>D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\bin\Release\LSB.LogMonitor.Service.Setup.msi</field><field>D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\LSB.LogMonitor.Service.Setup.wixproj</field><field>D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\bin\Release\LSB.LogMonitor.Service.Setup.wixpdb</field></row></table><table name="WixComplexReference" xmlns="http://schemas.microsoft.com/wix/2006/objects"><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*30"><field>*</field><field>5</field><field /><field>Complete</field><field>2</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*92"><field>Complete</field><field>1</field><field /><field>AppSettings</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*224"><field>Complete</field><field>1</field><field /><field>AppSettingsDevelopment</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*100"><field>Complete</field><field>1</field><field /><field>DepsJson</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*149"><field>Complete</field><field>1</field><field /><field>MicrosoftExtensionsConfiguration</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*153"><field>Complete</field><field>1</field><field /><field>MicrosoftExtensionsConfigurationAbstractions</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*173"><field>Complete</field><field>1</field><field /><field>MicrosoftExtensionsConfigurationBinder</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*169"><field>Complete</field><field>1</field><field /><field>MicrosoftExtensionsConfigurationCommandLine</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*165"><field>Complete</field><field>1</field><field /><field>MicrosoftExtensionsConfigurationEnvironmentVariables</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*161"><field>Complete</field><field>1</field><field /><field>MicrosoftExtensionsConfigurationFileExtensions</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*157"><field>Complete</field><field>1</field><field /><field>MicrosoftExtensionsConfigurationJson</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*113"><field>Complete</field><field>1</field><field /><field>MicrosoftExtensionsDI</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*117"><field>Complete</field><field>1</field><field /><field>MicrosoftExtensionsDIAbstractions</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*189"><field>Complete</field><field>1</field><field /><field>MicrosoftExtensionsFileProviders</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*193"><field>Complete</field><field>1</field><field /><field>MicrosoftExtensionsFileProvidersPhysical</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*197"><field>Complete</field><field>1</field><field /><field>MicrosoftExtensionsFileSystemGlobbing</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*109"><field>Complete</field><field>1</field><field /><field>MicrosoftExtensionsHosting</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*105"><field>Complete</field><field>1</field><field /><field>MicrosoftExtensionsHostingAbstractions</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*202"><field>Complete</field><field>1</field><field /><field>MicrosoftExtensionsHostingWindowsServices</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*121"><field>Complete</field><field>1</field><field /><field>MicrosoftExtensionsLogging</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*125"><field>Complete</field><field>1</field><field /><field>MicrosoftExtensionsLoggingAbstractions</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*133"><field>Complete</field><field>1</field><field /><field>MicrosoftExtensionsLoggingConfiguration</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*129"><field>Complete</field><field>1</field><field /><field>MicrosoftExtensionsLoggingConsole</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*145"><field>Complete</field><field>1</field><field /><field>MicrosoftExtensionsLoggingDebug</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*137"><field>Complete</field><field>1</field><field /><field>MicrosoftExtensionsLoggingEventLog</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*141"><field>Complete</field><field>1</field><field /><field>MicrosoftExtensionsLoggingEventSource</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*177"><field>Complete</field><field>1</field><field /><field>MicrosoftExtensionsOptions</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*181"><field>Complete</field><field>1</field><field /><field>MicrosoftExtensionsOptionsConfigurationExtensions</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*185"><field>Complete</field><field>1</field><field /><field>MicrosoftExtensionsPrimitives</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*96"><field>Complete</field><field>1</field><field /><field>RuntimeConfig</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*87"><field>Complete</field><field>1</field><field /><field>ServiceDll</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*64"><field>Complete</field><field>1</field><field /><field>ServiceExe</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*229"><field>Complete</field><field>1</field><field /><field>StartServiceScript</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*207"><field>Complete</field><field>1</field><field /><field>SystemDiagnosticsEventLog</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*211"><field>Complete</field><field>1</field><field /><field>SystemServiceProcessServiceController</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*215"><field>Complete</field><field>1</field><field /><field>SystemTextJson</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*219"><field>Complete</field><field>1</field><field /><field>YamlDotNet</field><field>1</field><field>0</field></row></table><table name="WixComponentGroup" xmlns="http://schemas.microsoft.com/wix/2006/objects"><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*62"><field>ServiceFiles</field></row></table><table name="WixFile" xmlns="http://schemas.microsoft.com/wix/2006/objects"><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*65"><field>ServiceExe</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net6.0\win-x64\publish\LSB.LogMonitor.Service.exe">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net6.0\win-x64\publish\LSB.LogMonitor.Service.exe</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*88"><field>ServiceDll</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net6.0\win-x64\publish\LSB.LogMonitor.Service.dll">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net6.0\win-x64\publish\LSB.LogMonitor.Service.dll</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*93"><field>AppSettings</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net6.0\win-x64\publish\appsettings.json">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net6.0\win-x64\publish\appsettings.json</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*97"><field>RuntimeConfig</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net6.0\win-x64\publish\LSB.LogMonitor.Service.runtimeconfig.json">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net6.0\win-x64\publish\LSB.LogMonitor.Service.runtimeconfig.json</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*101"><field>DepsJson</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net6.0\win-x64\publish\LSB.LogMonitor.Service.deps.json">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net6.0\win-x64\publish\LSB.LogMonitor.Service.deps.json</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*106"><field>MicrosoftExtensionsHostingAbstractions</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net6.0\win-x64\publish\Microsoft.Extensions.Hosting.Abstractions.dll">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net6.0\win-x64\publish\Microsoft.Extensions.Hosting.Abstractions.dll</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*110"><field>MicrosoftExtensionsHosting</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net6.0\win-x64\publish\Microsoft.Extensions.Hosting.dll">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net6.0\win-x64\publish\Microsoft.Extensions.Hosting.dll</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*114"><field>MicrosoftExtensionsDI</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net6.0\win-x64\publish\Microsoft.Extensions.DependencyInjection.dll">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net6.0\win-x64\publish\Microsoft.Extensions.DependencyInjection.dll</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*118"><field>MicrosoftExtensionsDIAbstractions</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net6.0\win-x64\publish\Microsoft.Extensions.DependencyInjection.Abstractions.dll">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net6.0\win-x64\publish\Microsoft.Extensions.DependencyInjection.Abstractions.dll</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*122"><field>MicrosoftExtensionsLogging</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net6.0\win-x64\publish\Microsoft.Extensions.Logging.dll">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net6.0\win-x64\publish\Microsoft.Extensions.Logging.dll</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*126"><field>MicrosoftExtensionsLoggingAbstractions</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net6.0\win-x64\publish\Microsoft.Extensions.Logging.Abstractions.dll">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net6.0\win-x64\publish\Microsoft.Extensions.Logging.Abstractions.dll</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*130"><field>MicrosoftExtensionsLoggingConsole</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net6.0\win-x64\publish\Microsoft.Extensions.Logging.Console.dll">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net6.0\win-x64\publish\Microsoft.Extensions.Logging.Console.dll</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*134"><field>MicrosoftExtensionsLoggingConfiguration</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net6.0\win-x64\publish\Microsoft.Extensions.Logging.Configuration.dll">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net6.0\win-x64\publish\Microsoft.Extensions.Logging.Configuration.dll</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*138"><field>MicrosoftExtensionsLoggingEventLog</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net6.0\win-x64\publish\Microsoft.Extensions.Logging.EventLog.dll">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net6.0\win-x64\publish\Microsoft.Extensions.Logging.EventLog.dll</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*142"><field>MicrosoftExtensionsLoggingEventSource</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net6.0\win-x64\publish\Microsoft.Extensions.Logging.EventSource.dll">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net6.0\win-x64\publish\Microsoft.Extensions.Logging.EventSource.dll</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*146"><field>MicrosoftExtensionsLoggingDebug</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net6.0\win-x64\publish\Microsoft.Extensions.Logging.Debug.dll">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net6.0\win-x64\publish\Microsoft.Extensions.Logging.Debug.dll</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*150"><field>MicrosoftExtensionsConfiguration</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net6.0\win-x64\publish\Microsoft.Extensions.Configuration.dll">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net6.0\win-x64\publish\Microsoft.Extensions.Configuration.dll</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*154"><field>MicrosoftExtensionsConfigurationAbstractions</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net6.0\win-x64\publish\Microsoft.Extensions.Configuration.Abstractions.dll">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net6.0\win-x64\publish\Microsoft.Extensions.Configuration.Abstractions.dll</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*158"><field>MicrosoftExtensionsConfigurationJson</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net6.0\win-x64\publish\Microsoft.Extensions.Configuration.Json.dll">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net6.0\win-x64\publish\Microsoft.Extensions.Configuration.Json.dll</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*162"><field>MicrosoftExtensionsConfigurationFileExtensions</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net6.0\win-x64\publish\Microsoft.Extensions.Configuration.FileExtensions.dll">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net6.0\win-x64\publish\Microsoft.Extensions.Configuration.FileExtensions.dll</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*166"><field>MicrosoftExtensionsConfigurationEnvironmentVariables</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net6.0\win-x64\publish\Microsoft.Extensions.Configuration.EnvironmentVariables.dll">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net6.0\win-x64\publish\Microsoft.Extensions.Configuration.EnvironmentVariables.dll</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*170"><field>MicrosoftExtensionsConfigurationCommandLine</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net6.0\win-x64\publish\Microsoft.Extensions.Configuration.CommandLine.dll">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net6.0\win-x64\publish\Microsoft.Extensions.Configuration.CommandLine.dll</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*174"><field>MicrosoftExtensionsConfigurationBinder</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net6.0\win-x64\publish\Microsoft.Extensions.Configuration.Binder.dll">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net6.0\win-x64\publish\Microsoft.Extensions.Configuration.Binder.dll</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*178"><field>MicrosoftExtensionsOptions</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net6.0\win-x64\publish\Microsoft.Extensions.Options.dll">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net6.0\win-x64\publish\Microsoft.Extensions.Options.dll</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*182"><field>MicrosoftExtensionsOptionsConfigurationExtensions</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net6.0\win-x64\publish\Microsoft.Extensions.Options.ConfigurationExtensions.dll">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net6.0\win-x64\publish\Microsoft.Extensions.Options.ConfigurationExtensions.dll</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*186"><field>MicrosoftExtensionsPrimitives</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net6.0\win-x64\publish\Microsoft.Extensions.Primitives.dll">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net6.0\win-x64\publish\Microsoft.Extensions.Primitives.dll</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*190"><field>MicrosoftExtensionsFileProviders</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net6.0\win-x64\publish\Microsoft.Extensions.FileProviders.Abstractions.dll">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net6.0\win-x64\publish\Microsoft.Extensions.FileProviders.Abstractions.dll</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*194"><field>MicrosoftExtensionsFileProvidersPhysical</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net6.0\win-x64\publish\Microsoft.Extensions.FileProviders.Physical.dll">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net6.0\win-x64\publish\Microsoft.Extensions.FileProviders.Physical.dll</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*198"><field>MicrosoftExtensionsFileSystemGlobbing</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net6.0\win-x64\publish\Microsoft.Extensions.FileSystemGlobbing.dll">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net6.0\win-x64\publish\Microsoft.Extensions.FileSystemGlobbing.dll</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*203"><field>MicrosoftExtensionsHostingWindowsServices</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net6.0\win-x64\publish\Microsoft.Extensions.Hosting.WindowsServices.dll">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net6.0\win-x64\publish\Microsoft.Extensions.Hosting.WindowsServices.dll</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*208"><field>SystemDiagnosticsEventLog</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net6.0\win-x64\publish\System.Diagnostics.EventLog.dll">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net6.0\win-x64\publish\System.Diagnostics.EventLog.dll</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*212"><field>SystemServiceProcessServiceController</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net6.0\win-x64\publish\System.ServiceProcess.ServiceController.dll">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net6.0\win-x64\publish\System.ServiceProcess.ServiceController.dll</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*216"><field>SystemTextJson</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net6.0\win-x64\publish\System.Text.Json.dll">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net6.0\win-x64\publish\System.Text.Json.dll</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*220"><field>YamlDotNet</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net6.0\win-x64\publish\YamlDotNet.dll">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net6.0\win-x64\publish\YamlDotNet.dll</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*225"><field>AppSettingsDevelopment</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net6.0\win-x64\publish\appsettings.Development.json">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net6.0\win-x64\publish\appsettings.Development.json</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*230"><field>StartServiceBat</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="start_service.bat">start_service.bat</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row></table><table name="WixGroup" xmlns="http://schemas.microsoft.com/wix/2006/objects"><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*31"><field>Complete</field><field>Feature</field><field>ServiceFiles</field><field>ComponentGroup</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*30"><field>*</field><field>Product</field><field>Complete</field><field>Feature</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*64"><field>ServiceFiles</field><field>ComponentGroup</field><field>ServiceExe</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*87"><field>ServiceFiles</field><field>ComponentGroup</field><field>ServiceDll</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*92"><field>ServiceFiles</field><field>ComponentGroup</field><field>AppSettings</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*96"><field>ServiceFiles</field><field>ComponentGroup</field><field>RuntimeConfig</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*100"><field>ServiceFiles</field><field>ComponentGroup</field><field>DepsJson</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*105"><field>ServiceFiles</field><field>ComponentGroup</field><field>MicrosoftExtensionsHostingAbstractions</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*109"><field>ServiceFiles</field><field>ComponentGroup</field><field>MicrosoftExtensionsHosting</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*113"><field>ServiceFiles</field><field>ComponentGroup</field><field>MicrosoftExtensionsDI</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*117"><field>ServiceFiles</field><field>ComponentGroup</field><field>MicrosoftExtensionsDIAbstractions</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*121"><field>ServiceFiles</field><field>ComponentGroup</field><field>MicrosoftExtensionsLogging</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*125"><field>ServiceFiles</field><field>ComponentGroup</field><field>MicrosoftExtensionsLoggingAbstractions</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*129"><field>ServiceFiles</field><field>ComponentGroup</field><field>MicrosoftExtensionsLoggingConsole</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*133"><field>ServiceFiles</field><field>ComponentGroup</field><field>MicrosoftExtensionsLoggingConfiguration</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*137"><field>ServiceFiles</field><field>ComponentGroup</field><field>MicrosoftExtensionsLoggingEventLog</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*141"><field>ServiceFiles</field><field>ComponentGroup</field><field>MicrosoftExtensionsLoggingEventSource</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*145"><field>ServiceFiles</field><field>ComponentGroup</field><field>MicrosoftExtensionsLoggingDebug</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*149"><field>ServiceFiles</field><field>ComponentGroup</field><field>MicrosoftExtensionsConfiguration</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*153"><field>ServiceFiles</field><field>ComponentGroup</field><field>MicrosoftExtensionsConfigurationAbstractions</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*157"><field>ServiceFiles</field><field>ComponentGroup</field><field>MicrosoftExtensionsConfigurationJson</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*161"><field>ServiceFiles</field><field>ComponentGroup</field><field>MicrosoftExtensionsConfigurationFileExtensions</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*165"><field>ServiceFiles</field><field>ComponentGroup</field><field>MicrosoftExtensionsConfigurationEnvironmentVariables</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*169"><field>ServiceFiles</field><field>ComponentGroup</field><field>MicrosoftExtensionsConfigurationCommandLine</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*173"><field>ServiceFiles</field><field>ComponentGroup</field><field>MicrosoftExtensionsConfigurationBinder</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*177"><field>ServiceFiles</field><field>ComponentGroup</field><field>MicrosoftExtensionsOptions</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*181"><field>ServiceFiles</field><field>ComponentGroup</field><field>MicrosoftExtensionsOptionsConfigurationExtensions</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*185"><field>ServiceFiles</field><field>ComponentGroup</field><field>MicrosoftExtensionsPrimitives</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*189"><field>ServiceFiles</field><field>ComponentGroup</field><field>MicrosoftExtensionsFileProviders</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*193"><field>ServiceFiles</field><field>ComponentGroup</field><field>MicrosoftExtensionsFileProvidersPhysical</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*197"><field>ServiceFiles</field><field>ComponentGroup</field><field>MicrosoftExtensionsFileSystemGlobbing</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*202"><field>ServiceFiles</field><field>ComponentGroup</field><field>MicrosoftExtensionsHostingWindowsServices</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*207"><field>ServiceFiles</field><field>ComponentGroup</field><field>SystemDiagnosticsEventLog</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*211"><field>ServiceFiles</field><field>ComponentGroup</field><field>SystemServiceProcessServiceController</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*215"><field>ServiceFiles</field><field>ComponentGroup</field><field>SystemTextJson</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*219"><field>ServiceFiles</field><field>ComponentGroup</field><field>YamlDotNet</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*224"><field>ServiceFiles</field><field>ComponentGroup</field><field>AppSettingsDevelopment</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*229"><field>ServiceFiles</field><field>ComponentGroup</field><field>StartServiceScript</field><field>Component</field></row></table><table name="WixSimpleReference" xmlns="http://schemas.microsoft.com/wix/2006/objects"><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*5"><field>Property</field><field>Manufacturer</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*5"><field>Property</field><field>ProductCode</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*5"><field>Property</field><field>ProductLanguage</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*5"><field>Property</field><field>ProductName</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*5"><field>Property</field><field>ProductVersion</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*5"><field>Property</field><field>UpgradeCode</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*31"><field>WixComponentGroup</field><field>ServiceFiles</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*35"><field>Directory</field><field>INSTALLFOLDER</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*41"><field>Directory</field><field>INSTALLFOLDER</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*47"><field>Directory</field><field>INSTALLFOLDER</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*54"><field>CustomAction</field><field>SetServiceTimeout</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*54"><field>WixAction</field><field>InstallExecuteSequence/InstallServices</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*55"><field>CustomAction</field><field>SetServiceConfig</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*55"><field>WixAction</field><field>InstallExecuteSequence/SetServiceTimeout</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*56"><field>CustomAction</field><field>SetServiceFailureActions</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*56"><field>WixAction</field><field>InstallExecuteSequence/SetServiceConfig</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*62"><field>Directory</field><field>INSTALLFOLDER</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*65"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*88"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*93"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*97"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*101"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*106"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*110"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*114"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*118"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*122"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*126"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*130"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*134"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*138"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*142"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*146"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*150"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*154"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*158"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*162"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*166"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*170"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*174"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*178"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*182"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*186"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*190"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*194"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*198"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*203"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*208"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*212"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*216"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*220"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*225"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*230"><field>Media</field><field>1</field></row></table></wixOutput></wixPdb>