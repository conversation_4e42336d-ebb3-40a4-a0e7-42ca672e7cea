# PowerShell script to check missing dependencies
$publishPath = "D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net6.0\win-x64\publish"
$wixFile = "Product.wxs"

Write-Host "🔍 Checking dependencies..." -ForegroundColor Yellow

if (!(Test-Path $publishPath)) {
    Write-Host "❌ Publish path not found: $publishPath" -ForegroundColor Red
    Write-Host "Run 'dotnet publish' first!" -ForegroundColor Red
    exit 1
}

# Get all DLL files from publish directory
$publishedFiles = Get-ChildItem -Path $publishPath -Filter "*.dll" -Recurse | ForEach-Object { $_.Name }
Write-Host "📦 Found $($publishedFiles.Count) DLL files in publish directory" -ForegroundColor Green

# Read WiX file and extract referenced DLLs
$wixContent = Get-Content $wixFile -Raw
$wixDlls = @()
$matches = [regex]::Matches($wixContent, 'Source="[^"]*\\([^\\]+\.dll)"')
foreach ($match in $matches) {
    $wixDlls += $match.Groups[1].Value
}

Write-Host "📋 Found $($wixDlls.Count) DLL references in WiX file" -ForegroundColor Green

# Find missing DLLs
$missingDlls = $publishedFiles | Where-Object { $_ -notin $wixDlls }
$extraDlls = $wixDlls | Where-Object { $_ -notin $publishedFiles }

Write-Host "`n🚨 MISSING DLLs in WiX (need to add):" -ForegroundColor Red
$missingDlls | ForEach-Object { Write-Host "  - $_" -ForegroundColor Red }

Write-Host "`n⚠️  EXTRA DLLs in WiX (not in publish):" -ForegroundColor Yellow
$extraDlls | ForEach-Object { Write-Host "  - $_" -ForegroundColor Yellow }

Write-Host "`n📊 SUMMARY:" -ForegroundColor Cyan
Write-Host "  Published DLLs: $($publishedFiles.Count)" -ForegroundColor White
Write-Host "  WiX DLLs: $($wixDlls.Count)" -ForegroundColor White
Write-Host "  Missing: $($missingDlls.Count)" -ForegroundColor Red
Write-Host "  Extra: $($extraDlls.Count)" -ForegroundColor Yellow

if ($missingDlls.Count -gt 0) {
    Write-Host "`n💡 RECOMMENDATION: Use Heat.exe to auto-generate components or add missing DLLs manually" -ForegroundColor Cyan
    Write-Host "   Run: build-auto.bat (uses Heat.exe)" -ForegroundColor Green
}
