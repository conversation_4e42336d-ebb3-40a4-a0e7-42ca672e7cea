using System;
using System.Collections.Generic;

namespace LSB.LogMonitor.Service.Models
{
    public class LogSummaryResponse
    {
        public DateTime Date { get; set; }
        public string ClientName { get; set; } = string.Empty;
        public List<LogFileInfo> LogFiles { get; set; } = new List<LogFileInfo>();
        public List<LogSummary> LogTypeSummaries { get; set; } = new List<LogSummary>();
        public Dictionary<string, int> LogTypeDistribution { get; set; } = new Dictionary<string, int>();
        public Dictionary<string, int> SourceNameDistribution { get; set; } = new Dictionary<string, int>();
        public int TotalLogEntries { get; set; }
        public SystemHealthSummary? HealthSummary { get; set; }
    }

    public class LogFileInfo
    {
        public string FileName { get; set; } = string.Empty;
        public int TotalEntries { get; set; }
        public DateTime? FirstEntryTime { get; set; }
        public DateTime? LastEntryTime { get; set; }
    }

    public class LogSummary
    {
        public string LogType { get; set; } = string.Empty;
        public int Count { get; set; }
        public DateTime FirstOccurrence { get; set; }
        public DateTime LastOccurrence { get; set; }
        public List<string> SampleMessages { get; set; } = new List<string>();
    }

    public class SystemHealthSummary
    {
        public int TotalErrors { get; set; }
        public int TotalWarnings { get; set; }
        public int TotalServices { get; set; }
        public int RunningServices { get; set; }
        public bool AllServicesRunning { get; set; }
        public string OverallStatus { get; set; } = string.Empty;
        public int TotalKeywordReports { get; set; }
        public int TotalSqsConversions { get; set; }
        public int TotalSqsTraffics { get; set; }
    }
}
