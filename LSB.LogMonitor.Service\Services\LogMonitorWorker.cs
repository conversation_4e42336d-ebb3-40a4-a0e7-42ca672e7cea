﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using LSB.LogMonitor.Service.Models;

namespace LSB.LogMonitor.Services
{
    public class LogMonitorWorker : BackgroundService
    {
        private readonly ILogger<LogMonitorWorker> _logger;
        private readonly IConfiguration _configuration;
        private readonly ILogService _logService;
        private readonly ITelegramService _telegramService;
        private readonly IConfigService _configService;
        private readonly TimeSpan _checkInterval;

        public LogMonitorWorker(
            ILogger<LogMonitorWorker> logger,
            IConfiguration configuration,
            ILogService logService,
            ITelegramService telegramService,
            IConfigService configService)
        {
            _logger = logger;
            _configuration = configuration;
            _logService = logService;
            _telegramService = telegramService;
            _configService = configService;

            // Get interval from config (default 1 hour)
            var intervalHours = _configuration.GetValue<double>("LogMonitor:CheckIntervalHours", 1.0);
            _checkInterval = TimeSpan.FromHours(intervalHours);
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("LSB Log Monitor Service started at: {time}", DateTimeOffset.Now);
            _logger.LogInformation("Check interval: {interval} hours", _checkInterval.TotalHours);

            // Comment out initial run to avoid double messages
            // try
            // {
            //     _logger.LogInformation("Running initial log check...");
            //     await CheckLogsForAllClients(stoppingToken);
            //     _logger.LogInformation("Initial log check completed");
            // }
            // catch (Exception ex)
            // {
            //     _logger.LogError(ex, "Error occurred during initial log check");
            // }

            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    await CheckLogsForAllClients(stoppingToken);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error occurred while checking logs");
                }

                // Wait for next check interval
                await Task.Delay(_checkInterval, stoppingToken);
            }
        }

        private async Task CheckLogsForAllClients(CancellationToken cancellationToken)
        {
            // Get AccName from config instead of Environment.MachineName
            var accName = await _configService.GetAccNameAsync();
            _logger.LogInformation("Starting log check on account: {AccName} at: {time}", accName, DateTimeOffset.Now);

            // Get available clients using existing method
            var clientNames = await _logService.GetAvailableClientNamesAsync(cancellationToken);

            if (!clientNames.Any())
            {
                _logger.LogWarning("No client directories found on account: {AccName}", accName);
                return;
            }

            _logger.LogInformation("Found {ClientCount} clients to check on account: {AccName}: {ClientNames}",
                clientNames.Length, accName, string.Join(", ", clientNames));

            var today = DateTime.Today; // Only check today's logs
            var clientsWithLogs = new List<ClientLogInfo>();

            // Check each client for today's logs
            foreach (var clientName in clientNames)
            {
                try
                {
                    // Use existing method to check if logs exist
                    var logsExist = await _logService.LogsExistAsync(today, clientName, cancellationToken);

                    if (logsExist)
                    {
                        _logger.LogInformation("Found logs for client {ClientName} on account: {AccName} on {Date}",
                            clientName, accName, today.ToString("yyyy-MM-dd"));

                        // Use existing method to generate summary
                        var summary = await _logService.GenerateLogSummaryAsync(today, clientName, cancellationToken);

                        _logger.LogInformation("Log summary for {ClientName} on {AccName}: {FileCount} files, {EntryCount} entries, {ErrorCount} errors",
                            clientName, accName, summary.LogFiles.Count, summary.TotalLogEntries, summary.HealthSummary?.TotalErrors ?? 0);

                        clientsWithLogs.Add(new ClientLogInfo
                        {
                            ClientName = clientName,
                            LogFileCount = summary.LogFiles.Count,
                            Summary = summary,
                            Date = today
                        });
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing client {ClientName} on account: {AccName}", clientName, accName);
                    // Continue with other clients even if one fails
                }
            }

            // Send Telegram notification if any clients have logs
            if (clientsWithLogs.Any())
            {
                _logger.LogInformation("Sending Telegram notification for {Count} clients with logs on account: {AccName}",
                    clientsWithLogs.Count, accName);

                await _telegramService.SendLogNotificationAsync(clientsWithLogs, today, cancellationToken);
                _logger.LogInformation("Log notification sent successfully for {Count} clients on account: {AccName}",
                    clientsWithLogs.Count, accName);
            }
            else
            {
                _logger.LogInformation("No logs found for any clients on account: {AccName} on {Date}", accName, today.ToString("yyyy-MM-dd"));
            }

            _logger.LogInformation("Log check completed on account: {AccName} at: {time}", accName, DateTimeOffset.Now);
        }
    }
}
