﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using LSB.LogMonitor.Service.Models;

namespace LSB.LogMonitor.Services
{
    public class LogMonitorWorker : BackgroundService
    {
        private readonly ILogger<LogMonitorWorker> _logger;
        private readonly IConfiguration _configuration;
        private readonly ILogService _logService;
        private readonly ITelegramService _telegramService;
        private readonly TimeSpan _checkInterval;

        public LogMonitorWorker(
            ILogger<LogMonitorWorker> logger,
            IConfiguration configuration,
            ILogService logService,
            ITelegramService telegramService)
        {
            _logger = logger;
            _configuration = configuration;
            _logService = logService;
            _telegramService = telegramService;

            // Get interval from config (default 1 hour)
            var intervalHours = _configuration.GetValue<double>("LogMonitor:CheckIntervalHours", 1.0);
            _checkInterval = TimeSpan.FromHours(intervalHours);
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("LSB Log Monitor Service started at: {time}", DateTimeOffset.Now);
            _logger.LogInformation("Check interval: {interval} hours", _checkInterval.TotalHours);

            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    await CheckLogsForAllClients(stoppingToken);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error occurred while checking logs");
                }

                // Wait for next check interval
                await Task.Delay(_checkInterval, stoppingToken);
            }
        }

        private async Task CheckLogsForAllClients(CancellationToken cancellationToken)
        {
            _logger.LogInformation("Starting log check at: {time}", DateTimeOffset.Now);

            // Get available clients using existing method
            var clientNames = await _logService.GetAvailableClientNamesAsync(cancellationToken);

            if (!clientNames.Any())
            {
                _logger.LogWarning("No client directories found");
                return;
            }

            var today = DateTime.Today; // Only check today's logs
            var clientsWithLogs = new List<ClientLogInfo>();

            // Check each client for today's logs
            foreach (var clientName in clientNames)
            {
                try
                {
                    // Use existing method to check if logs exist
                    var logsExist = await _logService.LogsExistAsync(today, clientName, cancellationToken);

                    if (logsExist)
                    {
                        _logger.LogInformation("Found logs for client {ClientName} on {Date}",
                            clientName, today.ToString("yyyy-MM-dd"));

                        // Use existing method to generate summary
                        var summary = await _logService.GenerateLogSummaryAsync(today, clientName, cancellationToken);

                        clientsWithLogs.Add(new ClientLogInfo
                        {
                            ClientName = clientName,
                            LogFileCount = summary.LogFiles.Count,
                            Summary = summary,
                            Date = today
                        });
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing client {ClientName}", clientName);
                    // Continue with other clients even if one fails
                }
            }

            // Send Telegram notification if any clients have logs
            if (clientsWithLogs.Any())
            {
                _logger.LogInformation("Sending Telegram notification for {Count} clients with logs",
                    clientsWithLogs.Count);

                await _telegramService.SendLogNotificationAsync(clientsWithLogs, today, cancellationToken);
            }
            else
            {
                _logger.LogInformation("No logs found for any clients on {Date}", today.ToString("yyyy-MM-dd"));
            }
        }
        }
    }
