﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using LSB.LogMonitor.Service.Contracts;
using LSB.LogMonitor.Service.Models;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace LSB.LogMonitor.Services
{
    public class TelegramService : ITelegramService
    {
        private readonly ILogger<TelegramService> _logger;
        private readonly HttpClient _httpClient;
        private readonly string _botToken;
        private readonly string _mainChatId;
        private readonly Dictionary<string, string> _clientChatIds;
        private readonly bool _enabled;
        private readonly bool _includeDetailedErrors;
        private readonly int _maxErrorsPerClient;
        private readonly HashSet<string> _allowedAccounts;
        private bool _configFileChecked = false;
        private const string ConfigPath = @"    ";

        private readonly IConfigService _configService;

        public TelegramService(ILogger<TelegramService> logger, IConfiguration configuration, IConfigService configService)
        {
            _logger = logger;
            _configService = configService;
            _httpClient = new HttpClient();

            // Load configuration from appsettings
            _botToken = configuration.GetValue<string>("Telegram:BotToken") ?? "";
            _mainChatId = configuration.GetValue<string>("Telegram:MainChatId") ?? "";
            _enabled = configuration.GetValue<bool>("Telegram:Enabled", true);
            _includeDetailedErrors = configuration.GetValue<bool>("Telegram:IncludeDetailedErrors", true);
            _maxErrorsPerClient = configuration.GetValue<int>("Telegram:MaxErrorsPerClient", 5);

            // Load allowed accounts from config file
            _allowedAccounts = LoadAllowedAccountsFromConfig();

            // Load client-specific chat IDs
            _clientChatIds = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase);
            var clientChannelsSection = configuration.GetSection("Telegram:ClientChannels");

            foreach (var clientChannel in clientChannelsSection.GetChildren())
            {
                var clientName = clientChannel.Key;
                var chatId = clientChannel.Value;
                if (!string.IsNullOrEmpty(chatId))
                {
                    _clientChatIds[clientName] = chatId;
                }
            }

            if (string.IsNullOrEmpty(_botToken) || string.IsNullOrEmpty(_mainChatId))
            {
                _logger.LogWarning("Telegram bot token or main chat ID not configured. Notifications will be disabled.");
                _enabled = false;
            }

            _logger.LogInformation("Telegram Service initialized with Main Channel and {ClientChannelCount} client channels",
                _clientChatIds.Count);
        }

        private HashSet<string> LoadAllowedAccountsFromConfig()
        {
            var allowedAccounts = new HashSet<string>(StringComparer.OrdinalIgnoreCase);

            try
            {
                if (File.Exists(ConfigPath))
                {
                    var json = File.ReadAllText(ConfigPath);
                    using var doc = JsonDocument.Parse(json);

                    if (doc.RootElement.TryGetProperty("AccName", out var accNameElement))
                    {
                        var accName = accNameElement.GetString();
                        if (!string.IsNullOrEmpty(accName))
                        {
                            allowedAccounts.Add(accName);
                            _logger.LogInformation("Loaded allowed account from config: {Account}", accName);
                        }
                        else
                        {
                            _logger.LogWarning("AccName found in config file but value is empty");
                            // SendConfigWarningNotification("AccName found in config file but value is empty");
                        }
                    }
                    else
                    {
                        _logger.LogWarning("AccName property not found in config file");
                        // SendConfigWarningNotification("AccName property not found in config file");
                    }
                }
                else
                {
                    _logger.LogWarning("Config file not found at {ConfigPath}", ConfigPath);
                    // SendConfigWarningNotification($"Config file not found at {ConfigPath}");
                }
            }
            catch (JsonException ex)
            {
                _logger.LogError(ex, "Invalid JSON format in config file");
                // SendConfigWarningNotification("Invalid JSON format in config file");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to load allowed accounts from config file");
                // SendConfigWarningNotification("Failed to load config file due to an error");
            }

            _configFileChecked = true;
            return allowedAccounts;
        }

        private async void SendConfigWarningNotification(string message)
        {
            if (!_enabled || string.IsNullOrEmpty(_mainChatId))
                return;

            try
            {
                var warningMessage = $"⚠️ **LSB Log Monitor Configuration Warning**\n\n" +
                                   $"**Issue**: {message}\n\n" +
                                   $"**Action Required**:\n" +
                                   $"1. Please create/update the config file at: `{ConfigPath}`\n" +
                                   $"2. Add your account name in JSON format:\n" +
                                   $"```json\n" +
                                   $"{{\n" +
                                   $"  \"AccName\": \"YOUR_ACCOUNT_NAME\"\n" +
                                   $"}}\n" +
                                   $"```\n" +
                                   $"3. Restart the service after updating the config file";

                await SendMessageAsync(_mainChatId, warningMessage, CancellationToken.None);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send config warning notification");
            }
        }

        private bool IsAccountAllowed(string clientName)
        {
            // On first check, if no config was loaded, send notification
            if (!_configFileChecked)
            {
                _logger.LogWarning("Config file was not checked properly before account validation");
                return false;
            }

            // If no accounts are specified in config, allow all
            if (_allowedAccounts.Count == 0)
            {
                _logger.LogInformation("No account restrictions configured, allowing all clients");
                return true;
            }

            // Check if client name matches any allowed account (case-insensitive)
            var isAllowed = _allowedAccounts.Contains(clientName);

            // Also check if the client name matches the machine name (for backward compatibility)
            if (!isAllowed && clientName.Equals(Environment.MachineName, StringComparison.OrdinalIgnoreCase))
            {
                _logger.LogInformation("Client {ClientName} matches machine name {MachineName}, allowing access",
                    clientName, Environment.MachineName);
                isAllowed = true;
            }

            if (!isAllowed)
            {
                _logger.LogInformation("Client {ClientName} is not in allowed accounts list {AllowedAccounts}",
                    clientName, string.Join(", ", _allowedAccounts));
            }
            else
            {
                _logger.LogInformation("Client {ClientName} is allowed", clientName);
            }

            return isAllowed;
        }
        public async Task SendLogNotificationAsync(List<ClientLogInfo> clientsWithLogs, DateTime date, CancellationToken cancellationToken = default)
        {
            if (!_enabled)
            {
                _logger.LogInformation("Telegram notifications are disabled");
                return;
            }

            try
            {
                // Filter clients by allowed accounts
                var allowedClients = clientsWithLogs
                    .Where(c => IsAccountAllowed(c.ClientName))
                    .ToList();

                if (!allowedClients.Any())
                {
                    _logger.LogInformation("No clients with allowed accounts detected - skipping Telegram notification");
                    return;
                }

                // Check if there are any errors to report
                var totalErrors = allowedClients.Sum(c => c.Summary.HealthSummary?.TotalErrors ?? 0);
                if (totalErrors > 0)
                {
                    _logger.LogInformation("Processing {ErrorCount} errors for notification", totalErrors);
                }

                // Check if there are any errors before sending notification
                var hasErrors = allowedClients.Any(c => c.Summary.HealthSummary?.TotalErrors > 0);
                var hasLogs = allowedClients.Any(c => c.Summary.TotalLogEntries > 0);

                if (!hasErrors && !hasLogs)
                {
                    return;
                }

                // 1. Send summary to MAIN CHANNEL with links to client channels
                await SendMainChannelSummary(allowedClients, date, cancellationToken);

                // 2. Send detailed logs to individual CLIENT CHANNELS
                await SendClientChannelDetails(allowedClients, date, cancellationToken);

                _logger.LogInformation("Telegram notifications sent successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send multi-channel Telegram notifications");
            }
        }

        public async Task SendTestMessageAsync(CancellationToken cancellationToken = default)
        {
            if (!_enabled)
            {
                _logger.LogWarning("Telegram is not enabled");
                return;
            }

            var accName = await _configService.GetAccNameAsync();

            // Test main channel with senior metrics info (adjusted for 60min monitoring)
            var testMessage = $"🔧 **LSB Log Monitor Service Test - MAIN CHANNEL**\n" +
                            $"⏰ Time: {DateTime.Now:yyyy-MM-dd HH:mm:ss}\n" +
                            $"🖥️ **Account**: {accName}\n" +
                            $"✅ Service is running correctly!\n" +
                            $"📊 Configured client channels: {_clientChatIds.Count}\n" +
                            $"⏱️ **Monitoring Interval: 60 minutes**\n" +
                            $"\n" +
                            $"🎯 **Senior Metrics Available:**\n" +
                            $"⚡ Hourly error rate tracking (errors/minute)\n" +
                            $"🔥 High error volume detection (>30 errors/hour)\n" +
                            $"📈 Service uptime monitoring\n" +
                            $"⏰ Log freshness detection (>2h stale alert)\n" +
                            $"⬇️ Downtime estimation\n" +
                            $"🚨 System health alerts\n" +
                            $"\n" +
                            $"💼 **Business Metrics Monitoring:**\n" +
                            $"📈 Keyword Reports tracking\n" +
                            $"🔄 SQS Conversions monitoring\n" +
                            $"🚦 SQS Traffics tracking\n" +
                            $"📅 **Daily Check: 10:00 UTC**";

            await SendMessageAsync(_mainChatId, testMessage, cancellationToken);

            // Test client channels with enhanced format (adjusted for 60min monitoring)
            foreach (var clientChannel in _clientChatIds)
            {
                if (!IsAccountAllowed(clientChannel.Key))
                {
                    _logger.LogInformation("Skipping test message for client {ClientName} - account not allowed", clientChannel.Key);
                    continue;
                }

                var clientTestMessage = $"🔧 **LSB Log Monitor Test - {clientChannel.Key.ToUpper()} CHANNEL**\n" +
                                      $"⏰ Time: {DateTime.Now:yyyy-MM-dd HH:mm:ss}\n" +
                                      $"🖥️ **Account**: {accName}\n" +
                                      $"✅ Client channel is configured correctly!\n" +
                                      $"⏱️ **Monitoring Interval: 60 minutes**\n" +
                                      $"\n" +
                                      $"📊 **Enhanced Monitoring:**\n" +
                                      $"⚡ Per-service error rates (hourly average)\n" +
                                      $"📈 Individual service uptime tracking\n" +
                                      $"⏰ Service-level log freshness (2h threshold)\n" +
                                      $"🚨 Priority-based recommendations\n" +
                                      $"📋 Trend analysis and pattern detection\n" +
                                      $"\n" +
                                      $"💼 **Business Metrics:**\n" +
                                      $"📈 Keyword Reports: Real-time tracking\n" +
                                      $"🔄 SQS Conversions: Processing monitoring\n" +
                                      $"🚦 SQS Traffics: Data flow tracking\n" +
                                      $"📅 **Daily Business Check: 10:00 UTC**\n" +
                                      $"\n" +
                                      $"🔗 **Integration**: Linked with main channel for system overview";

                await SendMessageAsync(clientChannel.Value, clientTestMessage, cancellationToken);
                await Task.Delay(500, cancellationToken); // Avoid rate limiting
            }
        }

        public async Task SendDailyBusinessMetricsAlert(List<ClientLogInfo> clientsWithIssues, DateTime date, CancellationToken cancellationToken = default)
        {
            if (!_enabled)
            {
                _logger.LogInformation("Telegram notifications are disabled");
                return;
            }

            try
            {
                // Filter clients by allowed accounts
                var allowedClients = clientsWithIssues
                    .Where(c => IsAccountAllowed(c.ClientName))
                    .ToList();

                _logger.LogInformation("Sending daily business metrics alert for {ClientCount} clients with issues", allowedClients.Count);

                // 1. Send main channel alert
                await SendMainChannelBusinessAlert(allowedClients, date, cancellationToken);

                // 2. Send detailed alerts to individual client channels
                await SendClientChannelBusinessAlerts(allowedClients, date, cancellationToken);

                _logger.LogInformation("Daily business metrics alerts sent successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send daily business metrics alerts");
            }
        }

        public async Task SendDailyBusinessMetricsHealthySummary(List<ClientLogInfo> healthyClients, DateTime date, CancellationToken cancellationToken = default)
        {
            if (!_enabled)
            {
                _logger.LogInformation("Telegram notifications are disabled");
                return;
            }

            try
            {
                // Filter clients by allowed accounts
                var allowedClients = healthyClients
                    .Where(c => IsAccountAllowed(c.ClientName))
                    .ToList();

                var accName = await _configService.GetAccNameAsync();
                var summaryMessage = BuildHealthyBusinessMetricsSummary(allowedClients, date, accName);
                await SendMessageAsync(_mainChatId, summaryMessage, cancellationToken);

                _logger.LogInformation("Daily healthy business metrics summary sent");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send daily healthy business metrics summary");
            }
        }

        private async Task SendMainChannelSummary(List<ClientLogInfo> clientsWithLogs, DateTime date, CancellationToken cancellationToken)
        {
            var accName = await _configService.GetAccNameAsync();
            var allMessages = BuildMultiPartLogSummary(clientsWithLogs, date, accName);

            if (allMessages.Any())
            {
                var overviewMessage = allMessages.First();

                if (overviewMessage.StartsWith("**[Part "))
                {
                    var lines = overviewMessage.Split('\n');
                    if (lines.Length > 2 && lines[0].StartsWith("**[Part "))
                    {
                        overviewMessage = string.Join('\n', lines.Skip(2));
                    }
                }

                await SendMessageAsync(_mainChatId, overviewMessage, cancellationToken);
            }
        }

        private async Task SendClientChannelDetails(List<ClientLogInfo> clientsWithLogs, DateTime date, CancellationToken cancellationToken)
        {
            var clientsWithErrors = clientsWithLogs
                .Where(c => c.Summary.HealthSummary?.TotalErrors > 0 && IsAccountAllowed(c.ClientName))
                .ToList();

            foreach (var client in clientsWithErrors)
            {
                if (!_clientChatIds.ContainsKey(client.ClientName))
                {
                    _logger.LogWarning("No Telegram channel configured for client: {ClientName}", client.ClientName);
                    continue;
                }

                var clientChatId = _clientChatIds[client.ClientName];
                var detailMessages = BuildErrorOnlyAnalysis(client, date);

                foreach (var message in detailMessages)
                {
                    await SendMessageAsync(clientChatId, message, cancellationToken);
                    await Task.Delay(1000, cancellationToken); // Rate limiting
                }

                _logger.LogInformation("Detailed logs sent to {ClientName} channel ({MessageCount} parts)",
                    client.ClientName, detailMessages.Count);
            }
        }

        private async Task SendMainChannelBusinessAlert(List<ClientLogInfo> clientsWithIssues, DateTime date, CancellationToken cancellationToken)
        {
            var accName = await _configService.GetAccNameAsync();
            var message = BuildMainChannelBusinessAlert(clientsWithIssues, date, accName);
            await SendMessageAsync(_mainChatId, message, cancellationToken);
        }

        private async Task SendClientChannelBusinessAlerts(List<ClientLogInfo> clientsWithIssues, DateTime date, CancellationToken cancellationToken)
        {
            var accName = await _configService.GetAccNameAsync();

            foreach (var client in clientsWithIssues)
            {
                if (!_clientChatIds.ContainsKey(client.ClientName) || !IsAccountAllowed(client.ClientName))
                {
                    _logger.LogWarning("No Telegram channel configured or account not allowed for client: {ClientName}", client.ClientName);
                    continue;
                }

                var clientChatId = _clientChatIds[client.ClientName];
                var message = BuildClientBusinessAlert(client, date, accName);

                await SendMessageAsync(clientChatId, message, cancellationToken);
                await Task.Delay(1000, cancellationToken); // Rate limiting

                _logger.LogInformation("Business metrics alert sent to {ClientName} channel", client.ClientName);
            }
        }

        private string BuildMainChannelBusinessAlert(List<ClientLogInfo> clientsWithIssues, DateTime date, string accName)
        {
            var sb = new StringBuilder();
            sb.AppendLine($"🚨 **DAILY BUSINESS METRICS ALERT - {accName}**");
            sb.AppendLine($"📅 **Date**: {date:yyyy-MM-dd}");
            sb.AppendLine($"⏰ **Check Time**: {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss} UTC");
            sb.AppendLine($"🖥️ **Account**: {accName}");
            sb.AppendLine($"🔍 **Daily Check**: 10:00 UTC Schedule");
            sb.AppendLine();

            sb.AppendLine($"❌ **{clientsWithIssues.Count} CLIENT(S) WITH BUSINESS DATA ISSUES**");
            sb.AppendLine();

            foreach (var client in clientsWithIssues.OrderBy(c => c.ClientName))
            {
                var health = client.Summary.HealthSummary;
                if (health != null)
                {
                    sb.AppendLine($"🏢 **{client.ClientName.ToUpper()}**");
                    sb.AppendLine($"   📊 **Keyword Reports**: {health.TotalKeywordReports} {(health.TotalKeywordReports == 0 ? "❌" : "✅")}");
                    sb.AppendLine($"   🔄 **SQS Conversions**: {health.TotalSqsConversions} {(health.TotalSqsConversions == 0 ? "❌" : "✅")}");
                    sb.AppendLine($"   🚦 **SQS Traffics**: {health.TotalSqsTraffics} {(health.TotalSqsTraffics == 0 ? "❌" : "✅")}");

                    // Link to client channel if available
                    if (_clientChatIds.ContainsKey(client.ClientName))
                    {
                        sb.AppendLine($"   📱 **[View Details →](https://t.me/c/{GetChannelUsername(_clientChatIds[client.ClientName])})**");
                    }
                    sb.AppendLine();
                }
            }

            sb.AppendLine("🚨 **CRITICAL**: One or more business metrics are at zero!");
            sb.AppendLine("⚡ **IMMEDIATE ACTION REQUIRED**");
            sb.AppendLine();
            sb.AppendLine("🔧 **Recommended Actions:**");
            sb.AppendLine("1. Check API connections and data pipelines");
            sb.AppendLine("2. Verify SQS queue processing");
            sb.AppendLine("3. Review keyword report generation");
            sb.AppendLine("4. Monitor individual client channels for details");
            sb.AppendLine();
            sb.AppendLine("📋 **Next Check**: Tomorrow at 10:00 UTC");

            return sb.ToString();
        }

        private string BuildClientBusinessAlert(ClientLogInfo client, DateTime date, string accName)
        {
            var sb = new StringBuilder();
            sb.AppendLine($"🚨 **BUSINESS DATA ALERT - {client.ClientName.ToUpper()} - {accName}**");
            sb.AppendLine($"📅 **Date**: {date:yyyy-MM-dd}");
            sb.AppendLine($"⏰ **Alert Time**: {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss} UTC");
            sb.AppendLine($"🖥️ **Account**: {accName}");
            sb.AppendLine();

            var health = client.Summary.HealthSummary;
            if (health != null)
            {
                sb.AppendLine("📊 **BUSINESS METRICS STATUS:**");
                sb.AppendLine();

                // Keyword Reports
                var keywordStatus = health.TotalKeywordReports > 0 ? "✅ **HEALTHY**" : "❌ **CRITICAL - NO DATA**";
                sb.AppendLine($"📈 **Keyword Reports**: {health.TotalKeywordReports}");
                sb.AppendLine($"   Status: {keywordStatus}");
                if (health.TotalKeywordReports == 0)
                {
                    sb.AppendLine($"   ⚠️ **No keyword reports received yesterday**");
                    sb.AppendLine($"   🔧 Check API connections and report generation");
                }
                sb.AppendLine();

                // SQS Conversions
                var conversionStatus = health.TotalSqsConversions > 0 ? "✅ **HEALTHY**" : "❌ **CRITICAL - NO DATA**";
                sb.AppendLine($"🔄 **SQS Conversions**: {health.TotalSqsConversions}");
                sb.AppendLine($"   Status: {conversionStatus}");
                if (health.TotalSqsConversions == 0)
                {
                    sb.AppendLine($"   ⚠️ **No conversion data received yesterday**");
                    sb.AppendLine($"   🔧 Check SQS queue processing and conversion tracking");
                }
                sb.AppendLine();

                // SQS Traffics
                var trafficStatus = health.TotalSqsTraffics > 0 ? "✅ **HEALTHY**" : "❌ **CRITICAL - NO DATA**";
                sb.AppendLine($"🚦 **SQS Traffics**: {health.TotalSqsTraffics}");
                sb.AppendLine($"   Status: {trafficStatus}");
                if (health.TotalSqsTraffics == 0)
                {
                    sb.AppendLine($"   ⚠️ **No traffic data received yesterday**");
                    sb.AppendLine($"   🔧 Check SQS traffic queue and data pipeline");
                }
                sb.AppendLine();

                // Priority actions based on missing data
                sb.AppendLine("🚨 **IMMEDIATE ACTIONS REQUIRED:**");
                sb.AppendLine();

                var missingData = new List<string>();
                if (health.TotalKeywordReports == 0) missingData.Add("Keyword Reports");
                if (health.TotalSqsConversions == 0) missingData.Add("SQS Conversions");
                if (health.TotalSqsTraffics == 0) missingData.Add("SQS Traffics");

                sb.AppendLine($"❌ **Missing Data**: {string.Join(", ", missingData)}");
                sb.AppendLine();

                sb.AppendLine("**Priority Checklist:**");
                if (health.TotalKeywordReports == 0)
                {
                    sb.AppendLine("□ **High Priority**: Check keyword report API and generation process");
                }
                if (health.TotalSqsConversions == 0)
                {
                    sb.AppendLine("□ **High Priority**: Verify SQS conversion queue processing");
                }
                if (health.TotalSqsTraffics == 0)
                {
                    sb.AppendLine("□ **High Priority**: Check SQS traffic data pipeline");
                }

                sb.AppendLine("□ Review API credentials and permissions");
                sb.AppendLine("□ Check queue health and message processing");
                sb.AppendLine("□ Verify data source connections");
                sb.AppendLine("□ Monitor for data recovery");
                sb.AppendLine();

                sb.AppendLine("⏰ **Business Impact**: Revenue and performance tracking affected");
                sb.AppendLine("📋 **Next Check**: Tomorrow at 10:00 UTC");
            }

            return sb.ToString();
        }

        private string BuildHealthyBusinessMetricsSummary(List<ClientLogInfo> healthyClients, DateTime date, string accName)
        {
            var sb = new StringBuilder();
            sb.AppendLine($"✅ **DAILY BUSINESS METRICS - ALL HEALTHY - {accName}**");
            sb.AppendLine($"📅 **Date**: {date:yyyy-MM-dd}");
            sb.AppendLine($"⏰ **Check Time**: {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss} UTC");
            sb.AppendLine($"🖥️ **Account**: {accName}");
            sb.AppendLine();

            sb.AppendLine("🎉 **All clients have healthy business metrics!**");
            sb.AppendLine();

            // Summary table
            sb.AppendLine("📊 **SUMMARY BY CLIENT:**");
            sb.AppendLine();

            int totalKeywordReports = 0;
            int totalSqsConversions = 0;
            int totalSqsTraffics = 0;

            foreach (var client in healthyClients.OrderBy(c => c.ClientName))
            {
                var health = client.Summary.HealthSummary;
                if (health != null)
                {
                    sb.AppendLine($"🏢 **{client.ClientName}**");
                    sb.AppendLine($"   📈 Keyword Reports: **{health.TotalKeywordReports}** ✅");
                    sb.AppendLine($"   🔄 SQS Conversions: **{health.TotalSqsConversions}** ✅");
                    sb.AppendLine($"   🚦 SQS Traffics: **{health.TotalSqsTraffics}** ✅");
                    sb.AppendLine();

                    totalKeywordReports += health.TotalKeywordReports;
                    totalSqsConversions += health.TotalSqsConversions;
                    totalSqsTraffics += health.TotalSqsTraffics;
                }
            }

            sb.AppendLine("📊 **SYSTEM TOTALS:**");
            sb.AppendLine($"📈 **Total Keyword Reports**: {totalKeywordReports:N0}");
            sb.AppendLine($"🔄 **Total SQS Conversions**: {totalSqsConversions:N0}");
            sb.AppendLine($"🚦 **Total SQS Traffics**: {totalSqsTraffics:N0}");
            sb.AppendLine();

            sb.AppendLine("✨ **Status**: All business data pipelines operational");
            sb.AppendLine("🔄 **Data Flow**: Normal processing across all clients");
            sb.AppendLine("📋 **Next Check**: Tomorrow at 10:00 UTC");

            return sb.ToString();
        }

        private List<string> BuildMultiPartLogSummary(List<ClientLogInfo> clientsWithLogs, DateTime date, string accName)
        {
            var messages = new List<string>();

            // PART 1: Overview - Enhanced with Business Metrics
            var part1 = new StringBuilder();

            var totalErrors = clientsWithLogs.Sum(c => c.Summary.HealthSummary?.TotalErrors ?? 0);
            var totalLogEntries = clientsWithLogs.Sum(c => c.Summary.TotalLogEntries);

            // Dynamic title based on content - match mẫu format
            if (totalErrors > 0)
            {
                part1.AppendLine($"🚨 LSB Error Alert - {date:yyyy-MM-dd}");
            }
            else
            {
                part1.AppendLine($"📊 LSB Daily Summary - {date:yyyy-MM-dd}");
            }
            part1.AppendLine($"⏰ Generated: {DateTime.Now:HH:mm:ss}");
            part1.AppendLine();

            var hasErrors = false;
            var hasBusinessIssues = false;

            foreach (var client in clientsWithLogs.OrderBy(c => c.ClientName))
            {
                var clientErrors = client.Summary.HealthSummary?.TotalErrors ?? 0;
                var health = client.Summary.HealthSummary;

                // Check for business data issues
                var businessIssues = new List<string>();
                if (health != null)
                {
                    if (health.TotalKeywordReports == 0) businessIssues.Add("Keywords");
                    if (health.TotalSqsConversions == 0) businessIssues.Add("Conversions");
                    if (health.TotalSqsTraffics == 0) businessIssues.Add("Traffics");
                }

                // Show ALL clients (changed behavior - show even if no errors)
                hasErrors = hasErrors || clientErrors > 0;
                hasBusinessIssues = hasBusinessIssues || businessIssues.Any();

                // Format theo mẫu: 🏢 MyLinh
                part1.AppendLine($"🏢 {client.ClientName}");

                if (clientErrors > 0)
                {
                    part1.AppendLine($"   🚨 {clientErrors} ERRORS detected");
                }

                if (businessIssues.Any())
                {
                    part1.AppendLine($"   💼 Business Issues: {string.Join(", ", businessIssues)} = 0");
                }

                part1.AppendLine($"   📁 Files: {client.LogFileCount}");
                part1.AppendLine($"   📝 Total entries: {client.Summary.TotalLogEntries}");

                if (health != null)
                {
                    part1.AppendLine($"   🔴 Status: {health.OverallStatus}");
                    part1.AppendLine($"   🟢 Running services: {health.RunningServices}/{health.TotalServices}");

                    // Add link to client channel if configured - ẩn URL trong text
                    if (_clientChatIds.ContainsKey(client.ClientName))
                    {
                        part1.AppendLine($"   📱 [View Details →](https://t.me/c/{GetChannelUsername(_clientChatIds[client.ClientName])})");
                    }
                }
                part1.AppendLine();
            }

            // Bỏ SYSTEM OVERVIEW để main channel chỉ hiển thị thông tin tóm tắt ngắn gọn

            messages.Add(part1.ToString());
            return messages;
        }

        private List<string> BuildErrorOnlyAnalysis(ClientLogInfo client, DateTime date)
        {
            var messages = new List<string>();
            const int maxLength = 3500;

            var sb = new StringBuilder();
            sb.AppendLine($"🚨 **Error Report - {client.ClientName}**");
            sb.AppendLine($"📅 {date:yyyy-MM-dd} ⏰ {DateTime.Now:HH:mm:ss}");
            sb.AppendLine();

            var health = client.Summary.HealthSummary!;

            // Get ONLY error messages from LogTypeSummaries
            var errorLogType = client.Summary.LogTypeSummaries.FirstOrDefault(s => s.LogType.Equals("Error", StringComparison.OrdinalIgnoreCase));

            if (errorLogType != null && errorLogType.SampleMessages.Any())
            {
                sb.AppendLine($"🚨 **{errorLogType.Count} Errors Found**");
                sb.AppendLine($"⏰ {errorLogType.FirstOccurrence:HH:mm:ss} - {errorLogType.LastOccurrence:HH:mm:ss}");
                sb.AppendLine();

                for (int i = 0; i < errorLogType.SampleMessages.Count; i++)
                {
                    var message = errorLogType.SampleMessages[i];
                    if (!string.IsNullOrWhiteSpace(message))
                    {
                        var cleanMessage = message.Trim().Trim('\'', '"');

                        sb.AppendLine($"🔴 **Error #{i + 1}**");
                        sb.AppendLine($"⏰ {errorLogType.LastOccurrence:yyyy-MM-dd HH:mm:ss}");
                        sb.AppendLine($"```");
                        sb.AppendLine(FormatErrorMessage(cleanMessage));
                        sb.AppendLine($"```");
                        sb.AppendLine();
                    }

                    if (sb.Length > maxLength)
                    {
                        messages.Add(sb.ToString());
                        sb.Clear();
                        sb.AppendLine($"🚨 **Error Report Continued - {client.ClientName}**");
                        sb.AppendLine();
                    }
                }
            }
            else
            {
                sb.AppendLine("✅ **No errors found**");
            }

            // Show which services have errors (simplified)
            var servicesWithErrors = health.Services
                .Where(s => s.ErrorCount > 0)
                .OrderByDescending(s => s.ErrorCount)
                .ToList();

            if (servicesWithErrors.Any())
            {
                sb.AppendLine($"⚠️ **Affected Services:**");
                foreach (var service in servicesWithErrors)
                {
                    sb.AppendLine($"🔧 {service.ServiceName}: {service.ErrorCount} errors");
                }
                sb.AppendLine();
            }



            if (sb.Length > 0)
            {
                messages.Add(sb.ToString());
            }

            return messages;
        }

        private string FormatErrorMessage(string message)
        {
            if (string.IsNullOrWhiteSpace(message))
                return "No message content";

            var cleaned = message.Trim();
            if (cleaned.StartsWith(">-"))
            {
                cleaned = cleaned.Substring(2).Trim();
            }

            const int maxLineLength = 80;
            if (cleaned.Length <= maxLineLength)
                return cleaned;

            var lines = new List<string>();
            var words = cleaned.Split(' ');
            var currentLine = new StringBuilder();

            foreach (var word in words)
            {
                if (currentLine.Length + word.Length + 1 > maxLineLength)
                {
                    if (currentLine.Length > 0)
                    {
                        lines.Add(currentLine.ToString());
                        currentLine.Clear();
                    }
                }

                if (currentLine.Length > 0)
                    currentLine.Append(" ");
                currentLine.Append(word);
            }

            if (currentLine.Length > 0)
            {
                lines.Add(currentLine.ToString());
            }

            return string.Join("\n", lines);
        }

        private string GetStatusEmoji(string status)
        {
            return status?.ToLower() switch
            {
                "healthy" => "🟢",
                "good" => "🟢",
                "active" => "🔵",
                "warning" => "🟡",
                "degraded" => "🟠",
                "error" => "🔴",
                "critical" => "🔴",
                "stopped" => "⚫",
                "inactive" => "⚫",
                "stale" => "🟤",
                _ => "⚪"
            };
        }

        private string GetSeverityEmoji(string severity)
        {
            return severity?.ToLower() switch
            {
                "critical" => "🚨",
                "high" => "🔴",
                "medium" => "🟡",
                "low" => "🟢",
                "info" => "ℹ️",
                _ => "⚪"
            };
        }

        private string GetChannelUsername(string chatId)
        {
            if (chatId.StartsWith("-100"))
            {
                return chatId.Substring(4);
            }
            return chatId;
        }

        private async Task SendMessageAsync(string chatId, string message, CancellationToken cancellationToken)
        {
            var url = $"https://api.telegram.org/bot{_botToken}/sendMessage";

            var payload = new
            {
                chat_id = chatId,
                text = message,
                parse_mode = "Markdown",
                disable_web_page_preview = true
            };

            var json = JsonSerializer.Serialize(payload);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync(url, content, cancellationToken);

            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync(cancellationToken);
                throw new Exception($"Telegram API error: {response.StatusCode}, {errorContent}");
            }
        }
    }
}