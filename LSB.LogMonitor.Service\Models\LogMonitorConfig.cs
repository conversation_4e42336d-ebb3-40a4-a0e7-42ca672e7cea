using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace LSB.LogMonitor.Service.Models
{
    public class LogMonitorConfig
    {
        public string AccName { get; set; } = string.Empty;
        public string MachineName { get; set; } = string.Empty;
        public string ChannelId { get; set; } = string.Empty;
        public DateTime LastUpdated { get; set; }
        public bool IsEnabled { get; set; } = true;
        public string Version { get; set; } = "1.0";
    }
}
