# LSB Telemetry Service Installer

## 🚨 IMPORTANT: How to Build

**DO NOT build this project directly from Visual Studio or using `dotnet build`.**

### ✅ Correct Way to Build:

```bash
cd LSB.LogMonitor.Service.Setup
build.bat
```

### ❌ Why Direct Build Fails:

The error `Unresolved reference to symbol 'WixComponentGroup:ServiceFiles'` occurs because:

1. **ServiceFiles.wxs is generated dynamically** by Heat.exe during build process
2. **Visual Studio/MSBuild** tries to compile Product.wxs before ServiceFiles.wxs exists
3. **build.bat** handles the correct build sequence:
   - Publish .NET service
   - Generate ServiceFiles.wxs with Heat.exe
   - Compile WiX sources
   - Create MSI

## 🔧 Build Process:

1. **Clean** previous builds
2. **Publish** .NET service with self-contained deployment
3. **Generate** ServiceFiles.wxs using Heat.exe (auto-detects all dependencies)
4. **Compile** WiX sources (Product.wxs + ServiceFiles.wxs)
5. **Create** MSI installer with ICE warnings suppressed

## 📁 Output:

- **LSBTelemetryService.msi** - Ready to install Windows Service

## 🚀 Installation:

```bash
# Install (run as Administrator)
LSBTelemetryService.msi

# Silent install
msiexec /i LSBTelemetryService.msi /quiet

# Uninstall
msiexec /x LSBTelemetryService.msi /quiet
```

## 🔍 Troubleshooting:

If you see the error `Unresolved reference to symbol 'WixComponentGroup:ServiceFiles'`:

1. **Use build.bat** instead of direct compilation
2. **Ensure Heat.exe runs** to generate ServiceFiles.wxs
3. **Check publish path** exists before running Heat.exe

## 📋 Files:

- **Product.wxs** - Main WiX installer definition
- **ServiceFiles.wxs** - Auto-generated by Heat.exe (dependencies)
- **build.bat** - Automated build script
- **ensure-servicefiles.bat** - Fallback script for missing ServiceFiles.wxs
