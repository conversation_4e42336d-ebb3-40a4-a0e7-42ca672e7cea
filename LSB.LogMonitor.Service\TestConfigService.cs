using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using LSB.LogMonitor.Services;

namespace LSB.LogMonitor.Service
{
    public static class TestConfigService
    {
        public static async Task RunTest()
        {
            Console.WriteLine("🔧 Testing ConfigService...");
            
            // Create a simple logger
            using var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
            var logger = loggerFactory.CreateLogger<ConfigService>();
            
            // Create ConfigService
            var configService = new ConfigService(logger);
            
            try
            {
                // Test GetAccNameAsync
                var accName = await configService.GetAccNameAsync();
                Console.WriteLine($"✅ AccName from config: {accName}");

                // Test GetConfigAsync
                var config = await configService.GetConfigAsync();
                Console.WriteLine($"✅ Config loaded:");
                Console.WriteLine($"   - AccName: {config.AccName}");
                Console.WriteLine($"   - MachineName: {config.MachineName}");
                Console.WriteLine($"   - IsEnabled: {config.IsEnabled}");
                Console.WriteLine($"   - Version: {config.Version}");
                Console.WriteLine($"   - LastUpdated: {config.LastUpdated}");
                
                Console.WriteLine("✅ ConfigService test completed successfully!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ ConfigService test failed: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                throw;
            }
        }
    }
}
