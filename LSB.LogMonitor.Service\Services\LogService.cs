using LSB.LogMonitor.Service.Models;
using LSB.LogMonitor.Service.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace LSB.LogMonitor.Services
{
    public class LogService : ILogService
    {
        private readonly ILogger<LogService> _logger;
        private readonly IConfigService _configService;
        private readonly string _logRootPath;

        public LogService(ILogger<LogService> logger, IOptions<LogMonitorOptions> options, IConfigService configService)
        {
            _logger = logger;
            _configService = configService;
            _logRootPath = options.Value.LogRootPath;
        }

        public async Task<LogSummaryResponse> GenerateLogSummaryAsync(DateTime date, string clientName, CancellationToken cancellationToken = default)
        {
            var response = new LogSummaryResponse
            {
                Date = date,
                ClientName = clientName,
                LogFiles = new List<LogFileInfo>(),
                LogTypeSummaries = new List<LogSummary>(),
                LogTypeDistribution = new Dictionary<string, int>(),
                SourceNameDistribution = new Dictionary<string, int>()
            };

            var dateFolderPath = Path.Combine(_logRootPath, date.ToString("yyyy-MM-dd"));
            var logFiles = new List<string>();

            if (Directory.Exists(_logRootPath))
            {
                logFiles.AddRange(Directory.GetFiles(_logRootPath, "*.log"));
            }

            if (Directory.Exists(dateFolderPath))
            {
                logFiles.AddRange(Directory.GetFiles(dateFolderPath, "*.log"));
            }

            if (logFiles.Count == 0)
            {
                return response;
            }

            var logEntries = new List<LogEntry>();
            var logTypeCounts = new Dictionary<string, int>();
            var logTypeMessages = new Dictionary<string, List<string>>();
            var logTypeFirstOccurrence = new Dictionary<string, DateTime>();
            var logTypeLastOccurrence = new Dictionary<string, DateTime>();
            var sourceNameCounts = new Dictionary<string, int>();
            var totalEntries = 0;

            foreach (var logFile in logFiles)
            {
                var fileName = Path.GetFileName(logFile);
                var fileEntries = new List<LogEntry>();
                DateTime? firstEntryTime = null;
                DateTime? lastEntryTime = null;

                try
                {
                    var logContent = await File.ReadAllTextAsync(logFile, cancellationToken);

                    if (string.IsNullOrWhiteSpace(logContent))
                    {
                        continue;
                    }

                    var entries = ParseYamlLogEntries(logContent);
                    var filteredEntries = entries.Where(e => e.Time.Date == date.Date).ToList();

                    if (filteredEntries.Any())
                    {
                        fileEntries.AddRange(filteredEntries);
                        firstEntryTime = filteredEntries.Min(e => e.Time);
                        lastEntryTime = filteredEntries.Max(e => e.Time);
                        totalEntries += filteredEntries.Count;

                        foreach (var entry in filteredEntries)
                        {
                            if (!logTypeCounts.ContainsKey(entry.LogType))
                            {
                                logTypeCounts[entry.LogType] = 0;
                                logTypeMessages[entry.LogType] = new List<string>();
                                logTypeFirstOccurrence[entry.LogType] = entry.Time;
                                logTypeLastOccurrence[entry.LogType] = entry.Time;
                            }

                            logTypeCounts[entry.LogType]++;

                            if (entry.Time < logTypeFirstOccurrence[entry.LogType])
                                logTypeFirstOccurrence[entry.LogType] = entry.Time;

                            if (entry.Time > logTypeLastOccurrence[entry.LogType])
                                logTypeLastOccurrence[entry.LogType] = entry.Time;

                            if (logTypeMessages[entry.LogType].Count < 5 && !string.IsNullOrEmpty(entry.Message))
                            {
                                var messageWithFile = $"[{fileName}] {entry.Message}";
                                logTypeMessages[entry.LogType].Add(messageWithFile);
                            }

                            var sourceName = !string.IsNullOrEmpty(entry.SourceName) ? entry.SourceName : "Unknown";
                            if (!sourceNameCounts.ContainsKey(sourceName))
                            {
                                sourceNameCounts[sourceName] = 0;
                            }
                            sourceNameCounts[sourceName]++;
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing log file: {FileName}", fileName);
                }

                response.LogFiles.Add(new LogFileInfo
                {
                    FileName = fileName,
                    TotalEntries = fileEntries.Count,
                    FirstEntryTime = firstEntryTime,
                    LastEntryTime = lastEntryTime
                });

                logEntries.AddRange(fileEntries);
            }

            foreach (var logType in logTypeCounts.Keys)
            {
                response.LogTypeSummaries.Add(new LogSummary
                {
                    LogType = logType,
                    Count = logTypeCounts[logType],
                    FirstOccurrence = logTypeFirstOccurrence[logType],
                    LastOccurrence = logTypeLastOccurrence[logType],
                    SampleMessages = logTypeMessages[logType]
                });
            }

            response.TotalLogEntries = totalEntries;
            response.LogTypeDistribution = logTypeCounts;
            response.SourceNameDistribution = sourceNameCounts;
            response.HealthSummary = GenerateHealthSummary(logEntries, logFiles.ToArray());

            return response;
        }

        public async Task<bool> LogsExistAsync(DateTime date, string clientName, CancellationToken cancellationToken = default)
        {
            var dateFolderPath = Path.Combine(_logRootPath, date.ToString("yyyy-MM-dd"));
            var logFiles = new List<string>();

            if (Directory.Exists(_logRootPath))
            {
                logFiles.AddRange(Directory.GetFiles(_logRootPath, "*.log"));
            }

            if (Directory.Exists(dateFolderPath))
            {
                logFiles.AddRange(Directory.GetFiles(dateFolderPath, "*.log"));
            }

            if (logFiles.Count == 0)
            {
                return false;
            }

            foreach (var logFile in logFiles)
            {
                try
                {
                    var logContent = await File.ReadAllTextAsync(logFile, cancellationToken);
                    if (!string.IsNullOrWhiteSpace(logContent))
                    {
                        var entries = ParseYamlLogEntries(logContent);
                        var entriesForDate = entries.Where(e => e.Time.Date == date.Date).ToList();

                        if (entriesForDate.Any())
                        {
                            return true;
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error checking log file: {FileName}", Path.GetFileName(logFile));
                }
            }

            return false;
        }

        public async Task<string[]> GetAvailableClientNamesAsync(CancellationToken cancellationToken = default)
        {
            var accName = await _configService.GetAccNameAsync();
            return new[] { accName };
        }

        private List<LogEntry> ParseYamlLogEntries(string content)
        {
            var entries = new List<LogEntry>();

            try
            {
                var lines = content.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);
                LogEntry? currentEntry = null;

                foreach (var line in lines)
                {
                    var trimmedLine = line.Trim();

                    if (trimmedLine == "- !Log")
                    {
                        if (currentEntry != null)
                        {
                            entries.Add(currentEntry);
                        }
                        currentEntry = new LogEntry();
                    }
                    else if (currentEntry != null && trimmedLine.Contains(":"))
                    {
                        var colonIndex = trimmedLine.IndexOf(':');
                        if (colonIndex > 0)
                        {
                            var key = trimmedLine.Substring(0, colonIndex).Trim();
                            var value = trimmedLine.Substring(colonIndex + 1).Trim();

                            switch (key)
                            {
                                case "LogType":
                                    currentEntry.LogType = value;
                                    break;
                                case "Time":
                                    if (DateTime.TryParse(value, out var time))
                                    {
                                        currentEntry.Time = time;
                                    }
                                    break;
                                case "Message":
                                    if (value.StartsWith(">-"))
                                    {
                                        currentEntry.Message = value.Substring(2).Trim();
                                    }
                                    else
                                    {
                                        currentEntry.Message = value;
                                    }

                                    if ((currentEntry.Message.Contains("Exception:") ||
                                         currentEntry.Message.Contains("Error:") ||
                                         currentEntry.Message.Contains("FAILED") ||
                                         currentEntry.Message.Contains("Forbidden")) &&
                                        currentEntry.LogType != "Error")
                                    {
                                        currentEntry.LogType = "Error";
                                    }
                                    break;
                                case "SourceName":
                                    currentEntry.SourceName = value;
                                    break;
                            }
                        }
                    }
                }

                if (currentEntry != null)
                {
                    entries.Add(currentEntry);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error parsing log content");
            }

            return entries;
        }

        private SystemHealthSummary GenerateHealthSummary(List<LogEntry> logEntries, string[] logFiles)
        {
            var healthSummary = new SystemHealthSummary();

            var errorCount = logEntries.Count(e => e.LogType.Equals("Error", StringComparison.OrdinalIgnoreCase));
            var warningCount = logEntries.Count(e => e.LogType.Equals("Warning", StringComparison.OrdinalIgnoreCase));

            healthSummary.TotalErrors = errorCount;
            healthSummary.TotalWarnings = warningCount;
            healthSummary.TotalServices = logFiles.Length;
            healthSummary.RunningServices = logFiles.Length;
            healthSummary.AllServicesRunning = true;
            healthSummary.OverallStatus = errorCount > 0 ? "Error" : warningCount > 0 ? "Warning" : "Healthy";

            return healthSummary;
        }

        private class LogEntry
        {
            public string LogType { get; set; } = string.Empty;
            public DateTime Time { get; set; }
            public string Message { get; set; } = string.Empty;
            public string SourceName { get; set; } = string.Empty;
        }
    }
}