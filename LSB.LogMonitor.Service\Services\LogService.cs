using LSB.LogMonitor.Service.Contracts;
using LSB.LogMonitor.Service.Configuration;
using LSB.LogMonitor.Services;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using YamlDotNet.Serialization;
using YamlDotNet.Serialization.NamingConventions;

namespace LSB.LogMonitor.Services
{
    public class LogService : ILogService
    {
        private readonly ILogger<LogService> _logger;
        private readonly LogMonitorOptions _options;
        private readonly string _logRootPath;
        private readonly IConfigService _configService;
        private readonly IDeserializer _yamlDeserializer;
        private static readonly Regex NumberExtractRegex = new Regex(@"\b(\d+)\b", RegexOptions.Compiled | RegexOptions.IgnoreCase);

        public LogService(ILogger<LogService> logger, IOptions<LogMonitorOptions> options, IConfigService configService)
        {
            _logger = logger;
            _options = options.Value;
            _logRootPath = _options.LogRootPath;
            _configService = configService;
            _yamlDeserializer = new DeserializerBuilder()
                .WithNamingConvention(CamelCaseNamingConvention.Instance)
                .Build();
        }

        public async Task<LogSummaryResponse> GenerateLogSummaryAsync(DateTime date, string clientName, CancellationToken cancellationToken = default)
        {
            var accName = await _configService.GetAccNameAsync();

            var response = new LogSummaryResponse
            {
                ClientName = accName,
                Date = date,
                LogTypeSummaries = new List<LogSummary>(),
                LogFiles = new List<LogFileInfo>(),
                LogTypeDistribution = new Dictionary<string, int>(),
                SourceNameDistribution = new Dictionary<string, int>()
            };

            var dateFolderPath = Path.Combine(_logRootPath, date.ToString("yyyy-MM-dd"));
            var logFiles = new List<string>();

            if (Directory.Exists(_logRootPath))
            {
                var rootLogFiles = Directory.GetFiles(_logRootPath, "*.log");
                logFiles.AddRange(rootLogFiles);
            }

            if (Directory.Exists(dateFolderPath))
            {
                var dateLogFiles = Directory.GetFiles(dateFolderPath, "*.log");
                logFiles.AddRange(dateLogFiles);
            }

            if (logFiles.Count == 0)
            {
                return response;
            }

            var logEntries = new List<LogEntry>();
            var logTypeCounts = new Dictionary<string, int>();
            var sourceNameCounts = new Dictionary<string, int>();
            var logTypeMessages = new Dictionary<string, List<string>>();
            var logTypeFirstOccurrence = new Dictionary<string, DateTime>();
            var logTypeLastOccurrence = new Dictionary<string, DateTime>();
            var sourceNameFirstOccurrence = new Dictionary<string, DateTime>();
            var sourceNameLastOccurrence = new Dictionary<string, DateTime>();

            int totalEntries = 0;

            foreach (var logFile in logFiles)
            {
                var fileName = Path.GetFileName(logFile);
                var fileEntries = new List<LogEntry>();
                DateTime? firstEntryTime = null;
                DateTime? lastEntryTime = null;

                try
                {
                    var logContent = await File.ReadAllTextAsync(logFile, cancellationToken);

                    if (string.IsNullOrWhiteSpace(logContent))
                    {
                        continue;
                    }

                    try
                    {
                        var entries = ParseYamlLogEntries(logContent);
                        var filteredEntries = entries.Where(e => e.Time.Date == date.Date).ToList();

                        if (filteredEntries.Any())
                        {
                            fileEntries.AddRange(filteredEntries);
                            firstEntryTime = filteredEntries.Min(e => e.Time);
                            lastEntryTime = filteredEntries.Max(e => e.Time);
                            totalEntries += filteredEntries.Count;

                            foreach (var entry in filteredEntries)
                            {
                                if (!logTypeCounts.ContainsKey(entry.LogType))
                                {
                                    logTypeCounts[entry.LogType] = 0;
                                    logTypeMessages[entry.LogType] = new List<string>();
                                    logTypeFirstOccurrence[entry.LogType] = entry.Time;
                                    logTypeLastOccurrence[entry.LogType] = entry.Time;
                                }

                                logTypeCounts[entry.LogType]++;

                                if (entry.Time < logTypeFirstOccurrence[entry.LogType])
                                    logTypeFirstOccurrence[entry.LogType] = entry.Time;

                                if (entry.Time > logTypeLastOccurrence[entry.LogType])
                                    logTypeLastOccurrence[entry.LogType] = entry.Time;

                                if (logTypeMessages[entry.LogType].Count < 5 && !string.IsNullOrEmpty(entry.Message))
                                {
                                    var messageWithFile = $"[{fileName}] {entry.Message}";
                                    logTypeMessages[entry.LogType].Add(messageWithFile);
                                }

                                var sourceName = !string.IsNullOrEmpty(entry.SourceName) ? entry.SourceName : "Unknown";
                                if (!sourceNameCounts.ContainsKey(sourceName))
                                {
                                    sourceNameCounts[sourceName] = 0;
                                    sourceNameFirstOccurrence[sourceName] = entry.Time;
                                    sourceNameLastOccurrence[sourceName] = entry.Time;
                                }

                                sourceNameCounts[sourceName]++;

                                if (entry.Time < sourceNameFirstOccurrence[sourceName])
                                    sourceNameFirstOccurrence[sourceName] = entry.Time;

                                if (entry.Time > sourceNameLastOccurrence[sourceName])
                                    sourceNameLastOccurrence[sourceName] = entry.Time;
                            }
                        }
                    }
                    catch (Exception parseEx)
                    {
                        _logger.LogError(parseEx, "Error parsing YAML entries from {FileName}", fileName);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing log file: {FileName}", fileName);
                }

                response.LogFiles.Add(new LogFileInfo
                {
                    FileName = fileName,
                    TotalEntries = fileEntries.Count,
                    FirstEntryTime = firstEntryTime,
                    LastEntryTime = lastEntryTime
                });

                logEntries.AddRange(fileEntries);
            }

            foreach (var logType in logTypeCounts.Keys)
            {
                response.LogTypeSummaries.Add(new LogSummary
                {
                    LogType = logType,
                    Count = logTypeCounts[logType],
                    FirstOccurrence = logTypeFirstOccurrence[logType],
                    LastOccurrence = logTypeLastOccurrence[logType],
                    SampleMessages = logTypeMessages[logType]
                });
            }

            response.TotalLogEntries = totalEntries;
            response.LogTypeDistribution = logTypeCounts;
            response.SourceNameDistribution = sourceNameCounts;
            response.HealthSummary = GenerateHealthSummary(logEntries, logFiles.ToArray());

            return response;
        }

        public async Task<bool> LogsExistAsync(DateTime date, string clientName, CancellationToken cancellationToken = default)
        {
            var dateFolderPath = Path.Combine(_logRootPath, date.ToString("yyyy-MM-dd"));
            var logFiles = new List<string>();

            if (Directory.Exists(_logRootPath))
            {
                var rootLogFiles = Directory.GetFiles(_logRootPath, "*.log");
                logFiles.AddRange(rootLogFiles);
            }

            if (Directory.Exists(dateFolderPath))
            {
                var dateLogFiles = Directory.GetFiles(dateFolderPath, "*.log");
                logFiles.AddRange(dateLogFiles);
            }

            if (logFiles.Count == 0)
            {
                return false;
            }

            foreach (var logFile in logFiles)
            {
                try
                {
                    var logContent = await File.ReadAllTextAsync(logFile, cancellationToken);
                    if (!string.IsNullOrWhiteSpace(logContent))
                    {
                        var entries = ParseYamlLogEntries(logContent);
                        var entriesForDate = entries.Where(e => e.Time.Date == date.Date).ToList();

                        if (entriesForDate.Any())
                        {
                            return true;
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error checking log file: {FileName}", Path.GetFileName(logFile));
                }
            }

            return false;
        }

        public async Task<string[]> GetAvailableClientNamesAsync(CancellationToken cancellationToken = default)
        {
            var accName = await _configService.GetAccNameAsync();
            return new[] { accName };
        }

        private List<LogEntry> ParseYamlLogEntries(string content)
        {
            var entries = new List<LogEntry>();

            try
            {
                var lines = content.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);
                LogEntry? currentEntry = null;

                foreach (var line in lines)
                {
                    var trimmedLine = line.Trim();

                    if (trimmedLine == "- !Log")
                    {
                        if (currentEntry != null)
                        {
                            entries.Add(currentEntry);
                        }
                        currentEntry = new LogEntry();
                    }
                    else if (currentEntry != null && trimmedLine.Contains(":"))
                    {
                        var colonIndex = trimmedLine.IndexOf(':');
                        if (colonIndex > 0)
                        {
                            var key = trimmedLine.Substring(0, colonIndex).Trim();
                            var value = trimmedLine.Substring(colonIndex + 1).Trim();

                            switch (key)
                            {
                                case "LogType":
                                    currentEntry.LogType = value;
                                    break;
                                case "Time":
                                    if (DateTime.TryParse(value, out var time))
                                    {
                                        currentEntry.Time = time;
                                    }
                                    break;
                                case "Message":
                                    if (value.StartsWith(">-"))
                                    {
                                        currentEntry.Message = value.Substring(2).Trim();
                                    }
                                    else
                                    {
                                        currentEntry.Message = value;
                                    }

                                    if ((currentEntry.Message.Contains("Exception:") ||
                                         currentEntry.Message.Contains("Error:") ||
                                         currentEntry.Message.Contains("FAILED") ||
                                         currentEntry.Message.Contains("Forbidden") ||
                                         currentEntry.Message.Contains("Invalid object name")) &&
                                        currentEntry.LogType != "Error")
                                    {
                                        currentEntry.LogType = "Error";
                                    }
                                    break;
                                case "SourceName":
                                    currentEntry.SourceName = value;
                                    break;
                            }
                        }
                    }
                }

                if (currentEntry != null)
                {
                    entries.Add(currentEntry);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error parsing log content");
            }

            return entries;
        }

        private class LogEntry
        {
            public string LogType { get; set; } = string.Empty;
            public DateTime Time { get; set; }
            public string Message { get; set; } = string.Empty;
            public string SourceName { get; set; } = string.Empty;
        }

        private SystemHealthSummary GenerateHealthSummary(List<LogEntry> logEntries, string[] logFiles)
        {
            var healthSummary = new SystemHealthSummary();
            var now = DateTime.Now;

            if (!logEntries.Any())
            {
                healthSummary.TotalServices = logFiles.Length;
                healthSummary.RunningServices = 0;
                healthSummary.StoppedServices = logFiles.Length;
                healthSummary.AllServicesRunning = false;
                healthSummary.OverallStatus = "Unknown";
                healthSummary.ErrorsPerMinute = 0;
                healthSummary.ServiceUptimePercentage = 0.0;
                healthSummary.TotalKeywordReports = 0;
                healthSummary.TotalSqsConversions = 0;
                healthSummary.TotalSqsTraffics = 0;
                return healthSummary;
            }

            var errorCount = logEntries.Count(e => e.LogType.Equals("Error", StringComparison.OrdinalIgnoreCase));
            var warningCount = logEntries.Count(e => e.LogType.Equals("Warning", StringComparison.OrdinalIgnoreCase));
            var infoCount = logEntries.Count(e => e.LogType.Equals("Info", StringComparison.OrdinalIgnoreCase));

            var keywordReports = ExtractBusinessMetric(logEntries, "Keyword Reports");
            var sqsConversions = ExtractBusinessMetric(logEntries, "SQS Conversions");
            var sqsTraffics = ExtractBusinessMetric(logEntries, "SQS Traffics");

            healthSummary.TotalServices = logFiles.Length;
            healthSummary.RunningServices = logFiles.Length - errorCount > 0 ? logFiles.Length - errorCount : 0;
            healthSummary.StoppedServices = errorCount;
            healthSummary.AllServicesRunning = errorCount == 0;
            healthSummary.OverallStatus = errorCount == 0 ? "Healthy" : "Issues";
            healthSummary.ErrorsPerMinute = errorCount;
            healthSummary.ServiceUptimePercentage = errorCount == 0 ? 100.0 : 50.0;
            healthSummary.TotalKeywordReports = keywordReports;
            healthSummary.TotalSqsConversions = sqsConversions;
            healthSummary.TotalSqsTraffics = sqsTraffics;

            return healthSummary;
        }

        private int ExtractBusinessMetric(List<LogEntry> logEntries, string metricName)
        {
            foreach (var entry in logEntries)
            {
                if (entry.Message.Contains(metricName, StringComparison.OrdinalIgnoreCase))
                {
                    var match = NumberExtractRegex.Match(entry.Message);
                    if (match.Success && int.TryParse(match.Value, out var value))
                    {
                        return value;
                    }
                }
            }
            return 0;
        }
    }
}
