﻿using LSB.LogMonitor.Service.Contracts;
using LSB.LogMonitor.Service.Configuration;
using LSB.LogMonitor.Services;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using YamlDotNet.Serialization;
using YamlDotNet.Serialization.NamingConventions;

namespace LSB.LogMonitor.Services
{
    public class LogService : ILogService
    {
        private readonly ILogger<LogService> _logger;
        private readonly LogMonitorOptions _options;
        private readonly string _logRootPath;
        private readonly IConfigService _configService;

        private readonly IDeserializer _yamlDeserializer;

        // Regex for extracting business metrics from log messages
        private static readonly Regex NumberExtractRegex =
            new Regex(@"\b(\d+)\b", RegexOptions.Compiled | RegexOptions.IgnoreCase);

        public LogService(ILogger<LogService> logger, IOptions<LogMonitorOptions> options, IConfigService configService)
        {
            _logger = logger;
            _options = options.Value;
            _logRootPath = _options.LogRootPath;
            _configService = configService;
            _yamlDeserializer = new DeserializerBuilder()
                .WithNamingConvention(CamelCaseNamingConvention.Instance)
                .Build();
        }

        public async Task<LogSummaryResponse> GenerateLogSummaryAsync(DateTime date, string clientName, CancellationToken cancellationToken = default)
        {
            // Get AccName from config instead of Environment.MachineName
            var accName = await _configService.GetAccNameAsync();

            var response = new LogSummaryResponse
            {
                ClientName = accName, // Sử dụng AccName từ config thay vì Environment.MachineName
                Date = date,
                LogTypeSummaries = new List<LogSummary>(),
                LogFiles = new List<LogFileInfo>(),
                LogTypeDistribution = new Dictionary<string, int>(),
                SourceNameDistribution = new Dictionary<string, int>()
            };

            // Check both root directory and date-specific subdirectory
            var dateFolderPath = Path.Combine(_logRootPath, date.ToString("yyyy-MM-dd"));

            var logFiles = new List<string>();

            // First check root directory
            if (Directory.Exists(_logRootPath))
            {
                var rootLogFiles = Directory.GetFiles(_logRootPath, "*.log");
                logFiles.AddRange(rootLogFiles);
            }
            else
            {
                _logger.LogWarning("Log root path does not exist: {LogRootPath}", _logRootPath);
            }

            // Then check date-specific subdirectory
            if (Directory.Exists(dateFolderPath))
            {
                var dateLogFiles = Directory.GetFiles(dateFolderPath, "*.log");
                logFiles.AddRange(dateLogFiles);
            }

            if (logFiles.Count == 0)
            {
                return response;
            }

            var logEntries = new List<LogEntry>();
            var logTypeCounts = new Dictionary<string, int>();
            var sourceNameCounts = new Dictionary<string, int>();
            var logTypeMessages = new Dictionary<string, List<string>>();
            var logTypeFirstOccurrence = new Dictionary<string, DateTime>();
            var logTypeLastOccurrence = new Dictionary<string, DateTime>();
            var sourceNameFirstOccurrence = new Dictionary<string, DateTime>();
            var sourceNameLastOccurrence = new Dictionary<string, DateTime>();

            int totalEntries = 0;

            foreach (var logFile in logFiles)
            {
                var fileName = Path.GetFileName(logFile);
                var fileEntries = new List<LogEntry>();
                DateTime? firstEntryTime = null;
                DateTime? lastEntryTime = null;

                try
                {
                    _logger.LogInformation("Reading log file: {LogFile}", logFile);
                    var logContent = await File.ReadAllTextAsync(logFile, cancellationToken);

                    if (string.IsNullOrWhiteSpace(logContent))
                    {
                        _logger.LogInformation("Empty log file: {FileName}", fileName);
                        continue;
                    }

                    _logger.LogInformation("Log file {FileName} content length: {Length} chars", fileName, logContent.Length);
                    var entries = ParseYamlLogEntries(logContent);

                    // Lọc log entries theo ngày được yêu cầu
                    var filteredEntries = entries.Where(e => e.Time.Date == date.Date).ToList();

                    if (filteredEntries.Any())
                    {
                        fileEntries.AddRange(filteredEntries);
                        firstEntryTime = filteredEntries.Min(e => e.Time);
                        lastEntryTime = filteredEntries.Max(e => e.Time);
                        totalEntries += filteredEntries.Count;

                        foreach (var entry in filteredEntries)
                        {
                            if (!logTypeCounts.ContainsKey(entry.LogType))
                            {
                                logTypeCounts[entry.LogType] = 0;
                                logTypeMessages[entry.LogType] = new List<string>();
                                logTypeFirstOccurrence[entry.LogType] = entry.Time;
                                logTypeLastOccurrence[entry.LogType] = entry.Time;
                            }

                            logTypeCounts[entry.LogType]++;

                            if (entry.Time < logTypeFirstOccurrence[entry.LogType])
                                logTypeFirstOccurrence[entry.LogType] = entry.Time;

                            if (entry.Time > logTypeLastOccurrence[entry.LogType])
                                logTypeLastOccurrence[entry.LogType] = entry.Time;

                            if (logTypeMessages[entry.LogType].Count < 5 && !string.IsNullOrEmpty(entry.Message))
                            {
                                logTypeMessages[entry.LogType].Add(entry.Message);

                                // Debug logging for error messages
                                if (entry.LogType.Equals("Error", StringComparison.OrdinalIgnoreCase))
                                {
                                    _logger.LogInformation("Found error message in {FileName}: '{Message}' at {Time}",
                                        fileName, entry.Message, entry.Time);
                                }
                            }

                            var sourceName = !string.IsNullOrEmpty(entry.SourceName) ? entry.SourceName : "Unknown";
                            if (!sourceNameCounts.ContainsKey(sourceName))
                            {
                                sourceNameCounts[sourceName] = 0;
                                sourceNameFirstOccurrence[sourceName] = entry.Time;
                                sourceNameLastOccurrence[sourceName] = entry.Time;
                            }

                            sourceNameCounts[sourceName]++;

                            if (entry.Time < sourceNameFirstOccurrence[sourceName])
                                sourceNameFirstOccurrence[sourceName] = entry.Time;

                            if (entry.Time > sourceNameLastOccurrence[sourceName])
                                sourceNameLastOccurrence[sourceName] = entry.Time;
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing log file: {FileName}", fileName);
                }

                response.LogFiles.Add(new LogFileInfo
                {
                    FileName = fileName,
                    TotalEntries = fileEntries.Count,
                    FirstEntryTime = firstEntryTime,
                    LastEntryTime = lastEntryTime
                });

                logEntries.AddRange(fileEntries);
            }

            foreach (var logType in logTypeCounts.Keys)
            {
                response.LogTypeSummaries.Add(new LogSummary
                {
                    LogType = logType,
                    Count = logTypeCounts[logType],
                    FirstOccurrence = logTypeFirstOccurrence[logType],
                    LastOccurrence = logTypeLastOccurrence[logType],
                    SampleMessages = logTypeMessages[logType]
                });
            }

            response.TotalLogEntries = totalEntries;
            response.LogTypeDistribution = logTypeCounts;
            response.SourceNameDistribution = sourceNameCounts;
            response.HealthSummary = GenerateHealthSummary(logEntries, logFiles.ToArray());

            return response;
        }

        public async Task<bool> LogsExistAsync(DateTime date, string clientName, CancellationToken cancellationToken = default)
        {
            // Check both root directory and date-specific subdirectory
            var dateFolderPath = Path.Combine(_logRootPath, date.ToString("yyyy-MM-dd"));

            var logFiles = new List<string>();

            // First check root directory
            if (Directory.Exists(_logRootPath))
            {
                var rootLogFiles = Directory.GetFiles(_logRootPath, "*.log");
                logFiles.AddRange(rootLogFiles);
            }

            // Then check date-specific subdirectory
            if (Directory.Exists(dateFolderPath))
            {
                var dateLogFiles = Directory.GetFiles(dateFolderPath, "*.log");
                logFiles.AddRange(dateLogFiles);
            }

            if (logFiles.Count == 0)
            {
                return false;
            }

            // Kiểm tra xem có log entries nào cho ngày được yêu cầu không
            foreach (var logFile in logFiles)
            {
                try
                {
                    var logContent = await File.ReadAllTextAsync(logFile, cancellationToken);
                    if (!string.IsNullOrWhiteSpace(logContent))
                    {
                        var entries = ParseYamlLogEntries(logContent);
                        var entriesForDate = entries.Where(e => e.Time.Date == date.Date).ToList();

                        if (entriesForDate.Any())
                        {
                            return true;
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error checking log file: {FileName}", Path.GetFileName(logFile));
                    // Continue checking other files
                }
            }

            return false;
        }

        public async Task<string[]> GetAvailableClientNamesAsync(CancellationToken cancellationToken = default)
        {
            // Trả về AccName từ config thay vì Environment.MachineName
            var accName = await _configService.GetAccNameAsync();
            return new[] { accName };
        }

        private List<LogEntry> ParseYamlLogEntries(string content)
        {
            var entries = new List<LogEntry>();

            try
            {
                var lines = content.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);
                LogEntry? currentEntry = null;

                foreach (var line in lines)
                {
                    var trimmedLine = line.Trim();

                    if (trimmedLine == "- !Log")
                    {
                        if (currentEntry != null)
                        {
                            entries.Add(currentEntry);
                        }
                        currentEntry = new LogEntry();
                    }
                    else if (currentEntry != null && trimmedLine.Contains(":"))
                    {
                        var colonIndex = trimmedLine.IndexOf(':');
                        if (colonIndex > 0)
                        {
                            var key = trimmedLine.Substring(0, colonIndex).Trim();
                            var value = trimmedLine.Substring(colonIndex + 1).Trim();

                            switch (key)
                            {
                                case "LogType":
                                    currentEntry.LogType = value;
                                    break;
                                case "Time":
                                    if (DateTime.TryParse(value, out var time))
                                    {
                                        currentEntry.Time = time;
                                    }
                                    break;
                                case "Message":
                                    if (value.StartsWith(">-"))
                                    {
                                        currentEntry.Message = value.Substring(2).Trim();

                                        if ((currentEntry.Message.Contains("Exception:") ||
                                             currentEntry.Message.Contains("Error:") ||
                                             currentEntry.Message.Contains("Forbidden") ||
                                             currentEntry.Message.Contains("Invalid object name") ||
                                             currentEntry.Message.Contains("SendAsyncCore") ||
                                             currentEntry.Message.Contains("Authority_AccessTokenFromRefreshToken")) &&
                                            currentEntry.LogType != "Error")
                                        {
                                            currentEntry.LogType = "Error";
                                        }

                                        if (currentEntry.Message.StartsWith("[DEBUG]") && currentEntry.LogType != "Debug")
                                        {
                                            currentEntry.LogType = "Debug";
                                        }

                                        if ((currentEntry.Message.Contains("Install LSB Hub Service") ||
                                             currentEntry.Message.Contains("Service LSB Hub Service is started") ||
                                             currentEntry.Message.Contains("Get the config")) &&
                                            currentEntry.LogType != "Info")
                                        {
                                            currentEntry.LogType = "Info";
                                        }
                                    }
                                    else
                                    {
                                        currentEntry.Message = value;

                                        if ((value.Contains("FAILED") ||
                                             value.Contains("Error") ||
                                             value.Contains("Exception") ||
                                             value.Contains("Forbidden") ||
                                             value.Contains("Invalid object name")) &&
                                            currentEntry.LogType != "Error")
                                        {
                                            currentEntry.LogType = "Error";
                                        }

                                        if (value.Contains("Stop service") || value.Contains("is Stopped") || value.Contains("stopping"))
                                        {
                                            if (currentEntry.LogType != "Warning")
                                            {
                                                currentEntry.LogType = "Warning";
                                            }
                                        }

                                        if ((value.Contains("PROCEED") ||
                                             value.Contains("RUN Rule") ||
                                             value.Contains("excecute per") ||
                                             value.Contains("Processing") ||
                                             value.Contains("Service is started")) &&
                                            currentEntry.LogType != "Info")
                                        {
                                            currentEntry.LogType = "Info";
                                        }
                                    }
                                    break;
                                case "SourceName":
                                    currentEntry.SourceName = value;
                                    break;
                            }
                        }
                    }
                }

                if (currentEntry != null)
                {
                    entries.Add(currentEntry);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error parsing log content");
            }

            return entries;
        }

        private class LogEntry
        {
            public string LogType { get; set; } = string.Empty;
            public DateTime Time { get; set; }
            public string Message { get; set; } = string.Empty;
            public string SourceName { get; set; } = string.Empty;
        }

        private SystemHealthSummary GenerateHealthSummary(List<LogEntry> logEntries, string[] logFiles)
        {
            var healthSummary = new SystemHealthSummary();
            var serviceEntries = new Dictionary<string, List<LogEntry>>();
            var allErrorPatterns = new Dictionary<string, ErrorPattern>();

            var now = DateTime.Now;

            if (!logEntries.Any() && logFiles.Any())
            {
                foreach (var logFile in logFiles)
                {
                    var serviceName = Path.GetFileNameWithoutExtension(logFile);
                    var serviceHealth = new ServiceHealthInfo
                    {
                        ServiceName = serviceName,
                        Status = "Unknown",
                        IsRunning = false,
                        ServiceErrorsPerMinute = 0,
                        ServiceTimeSinceLastLog = TimeSpan.MaxValue,
                        IsServiceStale = true,
                        ServiceUptimePercentage = 0.0,
                        LastReceivedKeywordReports = 0,
                        LastReceivedSqsConversions = 0,
                        LastReceivedSqsTraffics = 0
                    };

                    healthSummary.Services.Add(serviceHealth);
                }

                healthSummary.TotalServices = healthSummary.Services.Count;
                healthSummary.RunningServices = 0;
                healthSummary.StoppedServices = healthSummary.TotalServices;
                healthSummary.AllServicesRunning = false;
                healthSummary.OverallStatus = "Unknown";
                healthSummary.ErrorsPerMinute = 0;
                healthSummary.IsErrorSpike = false;
                healthSummary.LastErrorSpikeTime = null;
                healthSummary.ServiceUptimePercentage = 0.0;
                healthSummary.CurrentDowntime = TimeSpan.FromDays(1);
                healthSummary.TimeSinceLastLog = TimeSpan.MaxValue;
                healthSummary.IsLogStale = true;
                healthSummary.TotalKeywordReports = 0;
                healthSummary.TotalSqsConversions = 0;
                healthSummary.TotalSqsTraffics = 0;

                return healthSummary;
            }

            CalculateSystemSeniorMetrics(healthSummary, logEntries, now);
            CalculateBusinessMetrics(healthSummary, logEntries, now);

            var knownErrorPatterns = new List<(string pattern, string description, string severity, bool isUserImpacting, string recommendedAction)>
            {
                ("Execution Timeout Expired", "Database query timeout", "High", true, "Optimize SQL queries"),
                ("startDate to endDate range", "API date range limit exceeded", "Medium", true, "Modify date range"),
                ("OutOfMemory", "Out of memory exception", "Critical", true, "Increase memory"),
                ("Connection", "Connection-related issue", "High", true, "Check network"),
                ("The wait operation timed out", "Operation timeout", "Medium", true, "Review operations"),
                ("Response status code does not indicate success", "API request failed", "Medium", true, "Check API request"),
                ("FAILED ReportTask", "Report task failure", "High", true, "Check report parameters"),
                ("Stop service", "Service stopped", "Medium", false, "Verify service status"),
                ("is Stopped", "Service stopped status", "Medium", false, "Check service"),
                ("Services is stopping", "Service stopping", "Low", false, "Monitor service"),
                ("Win32Exception", "Windows system exception", "High", true, "Check event logs"),
                ("ComponentModel.Win32Exception", "Windows component error", "High", true, "Check component status"),
                ("Bad Request", "Bad API request", "Medium", true, "Review API request"),
                ("(403) Forbidden", "Access forbidden", "High", true, "Check permissions"),
                ("Invalid object name", "Database object not found", "High", true, "Check database schema"),
                ("SendAsyncCore", "HTTP request failure", "Medium", true, "Check network"),
                ("Authority_AccessTokenFromRefreshToken", "Token refresh failure", "High", true, "Check refresh token"),
                ("Install LSB Hub Service", "Service installation", "Info", false, "No action needed"),
                ("Service LSB Hub Service is started", "Service started", "Info", false, "No action needed"),
                ("Get the config", "Configuration loading", "Info", false, "No action needed"),
                ("KeywordReportService", "Keyword report issue", "High", true, "Check report pipeline"),
                ("SqsConversionProcessor", "SQS conversion failed", "High", true, "Verify queue processing"),
                ("SqsTrafficProcessor", "SQS traffic failed", "High", true, "Check data pipeline"),
                ("API rate limit", "API rate limit exceeded", "Medium", true, "Implement rate limiting"),
                ("Queue empty", "Processing queue empty", "Low", false, "Monitor queue")
            };

            foreach (var entry in logEntries)
            {
                var serviceName = !string.IsNullOrEmpty(entry.SourceName) ? entry.SourceName :
                                 Path.GetFileNameWithoutExtension(logFiles.FirstOrDefault(f => f.Contains(entry.SourceName)) ?? "Unknown");

                if (!serviceEntries.ContainsKey(serviceName))
                {
                    serviceEntries[serviceName] = new List<LogEntry>();
                }

                serviceEntries[serviceName].Add(entry);

                if (entry.LogType.Equals("Error", StringComparison.OrdinalIgnoreCase) && !string.IsNullOrEmpty(entry.Message))
                {
                    foreach (var (pattern, description, severity, isUserImpacting, recommendedAction) in knownErrorPatterns)
                    {
                        if (entry.Message.Contains(pattern, StringComparison.OrdinalIgnoreCase))
                        {
                            string patternKey = $"{serviceName}:{pattern}";

                            if (!allErrorPatterns.ContainsKey(patternKey))
                            {
                                allErrorPatterns[patternKey] = new ErrorPattern
                                {
                                    Pattern = pattern,
                                    Description = description,
                                    Count = 0,
                                    Severity = severity,
                                    FirstOccurrence = entry.Time,
                                    LastOccurrence = entry.Time,
                                    SampleMessages = new List<string>(),
                                    IsUserImpacting = isUserImpacting,
                                    RecommendedAction = recommendedAction
                                };
                            }

                            var errorPattern = allErrorPatterns[patternKey];
                            errorPattern.Count++;

                            if (entry.Time < errorPattern.FirstOccurrence)
                                errorPattern.FirstOccurrence = entry.Time;

                            if (entry.Time > errorPattern.LastOccurrence)
                                errorPattern.LastOccurrence = entry.Time;

                            if (errorPattern.SampleMessages.Count < 3 && !errorPattern.SampleMessages.Contains(entry.Message))
                                errorPattern.SampleMessages.Add(entry.Message);

                            break;
                        }
                    }
                }
            }

            foreach (var serviceName in serviceEntries.Keys)
            {
                var entries = serviceEntries[serviceName];
                var serviceHealth = new ServiceHealthInfo
                {
                    ServiceName = serviceName,
                    StartTime = entries.Where(e => e.Message.Contains("start", StringComparison.OrdinalIgnoreCase) ||
                                                e.Message.Contains("init", StringComparison.OrdinalIgnoreCase))
                                      .OrderBy(e => e.Time)
                                      .FirstOrDefault()?.Time,
                    LastActivityTime = entries.Max(e => e.Time),
                    ErrorCount = entries.Count(e => e.LogType.Equals("Error", StringComparison.OrdinalIgnoreCase)),
                    WarningCount = entries.Count(e => e.LogType.Equals("Warning", StringComparison.OrdinalIgnoreCase)),
                    InfoCount = entries.Count(e => e.LogType.Equals("Info", StringComparison.OrdinalIgnoreCase)),
                };

                CalculateServiceSeniorMetrics(serviceHealth, entries, now);
                CalculateServiceBusinessMetrics(serviceHealth, entries, now);

                var lastEntry = entries.OrderByDescending(e => e.Time).FirstOrDefault();
                var hasStopMessage = entries.Any(e => e.Message.Contains("stop", StringComparison.OrdinalIgnoreCase) ||
                                                   e.Message.Contains("shutdown", StringComparison.OrdinalIgnoreCase) ||
                                                   e.Message.Contains("terminated", StringComparison.OrdinalIgnoreCase) ||
                                                   e.Message.Contains("is Stopped", StringComparison.OrdinalIgnoreCase));

                bool isRunningMessage = lastEntry?.Message?.Contains("running", StringComparison.OrdinalIgnoreCase) ?? false;
                bool isStartedMessage = entries.Any(e => e.Message.Contains("Service is started", StringComparison.OrdinalIgnoreCase) &&
                                                   e.Time > DateTime.Now.AddHours(-24));
                bool hasRecentActivity = lastEntry != null &&
                                       lastEntry.Time > DateTime.Now.AddHours(-24) &&
                                       lastEntry.LogType?.Equals("Info", StringComparison.OrdinalIgnoreCase) == true;
                bool hasDebugActivity = entries.Any(e => (e.Message.Contains("excecute per", StringComparison.OrdinalIgnoreCase) ||
                                                       e.Message.Contains("RUN Rule", StringComparison.OrdinalIgnoreCase) ||
                                                       e.Message.Contains("PROCEED", StringComparison.OrdinalIgnoreCase) ||
                                                       e.Message.Contains("Processing", StringComparison.OrdinalIgnoreCase)) &&
                                                   e.Time > DateTime.Now.AddHours(-24));
                bool wasRecentlyInstalled = entries.Any(e => e.Message.Contains("Install LSB Hub Service", StringComparison.OrdinalIgnoreCase) &&
                                                       e.Time > DateTime.Now.AddHours(-24));

                serviceHealth.IsRunning = !hasStopMessage && (isRunningMessage || isStartedMessage || hasRecentActivity || hasDebugActivity || wasRecentlyInstalled);

                serviceHealth.RecentErrors = entries.Where(e => e.LogType.Equals("Error", StringComparison.OrdinalIgnoreCase))
                                                  .OrderByDescending(e => e.Time)
                                                  .Take(5)
                                                  .Select(e => e.Message)
                                                  .ToList();

                serviceHealth.RecentWarnings = entries.Where(e => e.LogType.Equals("Warning", StringComparison.OrdinalIgnoreCase))
                                                    .OrderByDescending(e => e.Time)
                                                    .Take(5)
                                                    .Select(e => e.Message)
                                                    .ToList();

                serviceHealth.CommonErrorPatterns = allErrorPatterns
                    .Where(kv => kv.Key.StartsWith($"{serviceName}:"))
                    .Select(kv => kv.Value)
                    .OrderByDescending(p => p.Count)
                    .ToList();

                if (serviceHealth.ErrorCount > 0)
                {
                    bool serviceCriticalErrors = serviceHealth.CommonErrorPatterns.Any(p => p.Severity == "Critical");
                    bool serviceHighSeverityErrors = serviceHealth.CommonErrorPatterns.Any(p => p.Severity == "High");

                    if (serviceCriticalErrors)
                        serviceHealth.Status = "Critical";
                    else if (serviceHighSeverityErrors)
                        serviceHealth.Status = serviceHealth.IsRunning ? "Warning" : "Critical";
                    else
                        serviceHealth.Status = serviceHealth.IsRunning ? "Warning" : "Critical";
                }
                else if (serviceHealth.WarningCount > 0)
                {
                    serviceHealth.Status = "Warning";
                }
                else if (serviceHealth.IsRunning)
                {
                    var lastActivity = entries.OrderByDescending(e => e.Time).FirstOrDefault()?.Time;
                    bool hasVeryRecentActivity = lastActivity.HasValue && lastActivity.Value > DateTime.Now.AddHours(-1);
                    bool hasRegularActivity = entries.Count(e => e.Time > DateTime.Now.AddHours(-6)) >= 3;
                    bool hasRuleExecution = entries.Any(e => (e.Message.Contains("RUN Rule", StringComparison.OrdinalIgnoreCase) ||
                                                            e.Message.Contains("PROCEED", StringComparison.OrdinalIgnoreCase)) &&
                                                        e.Time > DateTime.Now.AddHours(-3));
                    bool hasProcessingActivity = entries.Any(e => (e.Message.Contains("Processing", StringComparison.OrdinalIgnoreCase) ||
                                                              e.Message.Contains("excecute per", StringComparison.OrdinalIgnoreCase)) &&
                                                          e.Time > DateTime.Now.AddHours(-3));

                    if (hasVeryRecentActivity && (hasRuleExecution || hasProcessingActivity || hasRegularActivity))
                    {
                        serviceHealth.Status = "Healthy";
                    }
                    else if (hasVeryRecentActivity)
                    {
                        serviceHealth.Status = "Active";
                    }
                    else if (lastActivity.HasValue && lastActivity.Value > DateTime.Now.AddHours(-6))
                    {
                        serviceHealth.Status = "Idle";
                    }
                    else if (lastActivity.HasValue && lastActivity.Value > DateTime.Now.AddHours(-24))
                    {
                        serviceHealth.Status = "Stale";
                    }
                    else
                    {
                        serviceHealth.Status = "Inactive";
                    }
                }
                else if (entries.Any(e => e.Message.Contains("Stop service", StringComparison.OrdinalIgnoreCase) ||
                                       e.Message.Contains("is Stopped", StringComparison.OrdinalIgnoreCase)))
                {
                    serviceHealth.Status = "Stopped";
                }
                else
                {
                    serviceHealth.Status = "Unknown";
                }

                healthSummary.Services.Add(serviceHealth);
            }

            healthSummary.TotalServices = healthSummary.Services.Count;
            healthSummary.RunningServices = healthSummary.Services.Count(s => s.IsRunning);
            healthSummary.StoppedServices = healthSummary.Services.Count(s => s.Status == "Stopped");
            healthSummary.IdleServices = healthSummary.Services.Count(s => s.Status == "Idle");
            healthSummary.StaleServices = healthSummary.Services.Count(s => s.Status == "Stale");
            healthSummary.ActiveServices = healthSummary.Services.Count(s => s.Status == "Active");
            healthSummary.InactiveServices = healthSummary.Services.Count(s => s.Status == "Inactive");
            healthSummary.CriticalServices = healthSummary.Services.Count(s => s.Status == "Critical");
            healthSummary.WarningServices = healthSummary.Services.Count(s => s.Status == "Warning");
            healthSummary.HealthyServices = healthSummary.Services.Count(s => s.Status == "Healthy");
            healthSummary.AllServicesRunning = healthSummary.RunningServices == healthSummary.TotalServices;

            // Calculate service errors
            var serviceErrors = healthSummary.Services.Sum(s => s.ErrorCount);

            // Calculate business errors (1 error if ANY business metric is 0)
            var businessErrors = 0;
            if (healthSummary.TotalKeywordReports == 0 ||
                healthSummary.TotalSqsConversions == 0 ||
                healthSummary.TotalSqsTraffics == 0)
            {
                businessErrors = 1; // Only 1 business error regardless of how many metrics are 0
            }

            // Total errors = service errors + business errors
            healthSummary.TotalErrors = serviceErrors + businessErrors;
            healthSummary.TotalWarnings = healthSummary.Services.Sum(s => s.WarningCount);
            healthSummary.LastUpdated = DateTime.Now;

            var statusDistribution = healthSummary.Services
                .GroupBy(s => s.Status)
                .ToDictionary(g => g.Key, g => g.Count());
            healthSummary.ServiceStatusDistribution = statusDistribution;

            healthSummary.LastActivityByService = healthSummary.Services
                .ToDictionary(s => s.ServiceName, s => serviceEntries.ContainsKey(s.ServiceName) ?
                    serviceEntries[s.ServiceName].OrderByDescending(e => e.Time).FirstOrDefault()?.Time : null);

            var allPatterns = allErrorPatterns.Values.ToList();
            healthSummary.TotalUniqueErrorPatterns = allPatterns.Count;

            healthSummary.MostCommonErrors = allPatterns
                .OrderByDescending(p => p.Count)
                .Take(5)
                .ToList();

            healthSummary.MostSevereErrors = allPatterns
                .Where(p => p.Severity == "Critical" || p.Severity == "High")
                .OrderByDescending(p => p.Severity == "Critical" ? 1 : 0)
                .ThenByDescending(p => p.Count)
                .Take(5)
                .ToList();

            healthSummary.UserImpactingErrors = allPatterns
                .Where(p => p.IsUserImpacting)
                .OrderByDescending(p => p.Count)
                .Take(5)
                .ToList();

            CalculateSystemUptimeMetrics(healthSummary);
            FinalizeBusinessMetricsSummary(healthSummary);
            DetermineOverallStatusWithBusinessMetrics(healthSummary, allPatterns);

            return healthSummary;
        }

        private void CalculateSystemSeniorMetrics(SystemHealthSummary healthSummary, List<LogEntry> logEntries, DateTime now)
        {
            var mostRecentLogTime = logEntries.Any() ? logEntries.Max(e => e.Time) : (DateTime?)null;

            if (mostRecentLogTime.HasValue)
            {
                healthSummary.TimeSinceLastLog = now - mostRecentLogTime.Value;
                healthSummary.IsLogStale = healthSummary.TimeSinceLastLog.TotalMinutes > 30;
            }
            else
            {
                healthSummary.TimeSinceLastLog = TimeSpan.MaxValue;
                healthSummary.IsLogStale = true;
            }

            var fiveMinutesAgo = now.AddMinutes(-5);
            var recentErrors = logEntries.Where(e =>
                e.LogType.Equals("Error", StringComparison.OrdinalIgnoreCase) &&
                e.Time >= fiveMinutesAgo).ToList();

            healthSummary.ErrorsPerMinute = recentErrors.Count / 5.0;
            healthSummary.IsErrorSpike = recentErrors.Count >= 10;

            if (healthSummary.IsErrorSpike)
            {
                healthSummary.LastErrorSpikeTime = recentErrors.Max(e => e.Time);
            }
            else
            {
                healthSummary.LastErrorSpikeTime = null;
            }

            _logger.LogDebug("System metrics calculated: ErrorsPerMinute={ErrorsPerMinute}, IsErrorSpike={IsErrorSpike}",
                healthSummary.ErrorsPerMinute, healthSummary.IsErrorSpike);
        }

        private void CalculateServiceSeniorMetrics(ServiceHealthInfo serviceHealth, List<LogEntry> serviceEntries, DateTime now)
        {
            var fiveMinutesAgo = now.AddMinutes(-5);
            var serviceRecentErrors = serviceEntries.Where(e =>
                e.LogType.Equals("Error", StringComparison.OrdinalIgnoreCase) &&
                e.Time >= fiveMinutesAgo).ToList();

            serviceHealth.ServiceErrorsPerMinute = serviceRecentErrors.Count / 5.0;

            var serviceLastLog = serviceEntries.Any() ? serviceEntries.Max(e => e.Time) : (DateTime?)null;

            if (serviceLastLog.HasValue)
            {
                serviceHealth.ServiceTimeSinceLastLog = now - serviceLastLog.Value;
                serviceHealth.IsServiceStale = serviceHealth.ServiceTimeSinceLastLog.TotalMinutes > 30;
            }
            else
            {
                serviceHealth.ServiceTimeSinceLastLog = TimeSpan.MaxValue;
                serviceHealth.IsServiceStale = true;
            }

            if (serviceHealth.IsRunning && !serviceHealth.IsServiceStale)
            {
                serviceHealth.ServiceUptimePercentage = 100.0;
            }
            else if (serviceHealth.IsRunning && serviceHealth.IsServiceStale)
            {
                serviceHealth.ServiceUptimePercentage = 75.0;
            }
            else if (!serviceHealth.IsRunning && !serviceHealth.IsServiceStale)
            {
                serviceHealth.ServiceUptimePercentage = 50.0;
            }
            else
            {
                serviceHealth.ServiceUptimePercentage = 0.0;
            }

            _logger.LogDebug("Service {ServiceName} metrics: ErrorsPerMinute={ErrorsPerMinute}",
                serviceHealth.ServiceName, serviceHealth.ServiceErrorsPerMinute);
        }

        private void CalculateSystemUptimeMetrics(SystemHealthSummary healthSummary)
        {
            if (healthSummary.Services.Any())
            {
                healthSummary.ServiceUptimePercentage = healthSummary.Services.Average(s => s.ServiceUptimePercentage);
            }
            else
            {
                healthSummary.ServiceUptimePercentage = 0.0;
            }

            var downServices = healthSummary.Services.Count(s => !s.IsRunning || s.IsServiceStale);
            var totalServices = healthSummary.Services.Count;

            if (downServices > 0 && totalServices > 0)
            {
                var downServiceRatio = (double)downServices / totalServices;
                if (downServiceRatio > 0.5)
                {
                    healthSummary.CurrentDowntime = TimeSpan.FromMinutes(downServiceRatio * 60);
                }
                else
                {
                    healthSummary.CurrentDowntime = TimeSpan.FromMinutes(downServices * 5);
                }
            }
            else
            {
                healthSummary.CurrentDowntime = TimeSpan.Zero;
            }
        }

        private void CalculateBusinessMetrics(SystemHealthSummary healthSummary, List<LogEntry> logEntries, DateTime now)
        {
            var today = now.Date;
            var todayEntries = logEntries.Where(e => e.Time.Date == today).ToList();

            foreach (var entry in todayEntries)
            {
                var message = entry.Message?.ToLower() ?? "";

                if (message.Contains("keyword report") || message.Contains("report generated") ||
                    message.Contains("keyword data processed") || message.Contains("keyword") && message.Contains("processed"))
                {
                    var count = ExtractCountFromMessage(entry.Message, "report");
                    healthSummary.TotalKeywordReports += count;
                }

                if (message.Contains("sqs conversion") || message.Contains("conversion processed") ||
                    message.Contains("conversion data received") || message.Contains("conversion") && message.Contains("processed"))
                {
                    var count = ExtractCountFromMessage(entry.Message, "conversion");
                    healthSummary.TotalSqsConversions += count;
                }

                if (message.Contains("sqs traffic") || message.Contains("traffic processed") ||
                    message.Contains("traffic data received") || message.Contains("traffic") && message.Contains("processed"))
                {
                    var count = ExtractCountFromMessage(entry.Message, "traffic");
                    healthSummary.TotalSqsTraffics += count;
                }
            }
        }

        private void CalculateServiceBusinessMetrics(ServiceHealthInfo serviceHealth, List<LogEntry> serviceEntries, DateTime now)
        {
            var today = now.Date;
            var todayEntries = serviceEntries.Where(e => e.Time.Date == today).ToList();

            foreach (var entry in todayEntries)
            {
                var message = entry.Message?.ToLower() ?? "";

                if (message.Contains("keyword report") || message.Contains("report generated") ||
                    message.Contains("keyword") && message.Contains("processed"))
                {
                    var count = ExtractCountFromMessage(entry.Message, "report");
                    serviceHealth.LastReceivedKeywordReports += count;
                }

                if (message.Contains("sqs conversion") || message.Contains("conversion processed") ||
                    message.Contains("conversion") && message.Contains("processed"))
                {
                    var count = ExtractCountFromMessage(entry.Message, "conversion");
                    serviceHealth.LastReceivedSqsConversions += count;
                }

                if (message.Contains("sqs traffic") || message.Contains("traffic processed") ||
                    message.Contains("traffic") && message.Contains("processed"))
                {
                    var count = ExtractCountFromMessage(entry.Message, "traffic");
                    serviceHealth.LastReceivedSqsTraffics += count;
                }
            }
        }

        private int ExtractCountFromMessage(string message, string context)
        {
            if (string.IsNullOrEmpty(message))
                return 1;

            try
            {
                var patterns = new[]
                {
                    @"processed\s+(\d+)",
                    @"received\s+(\d+)",
                    @"generated\s+(\d+)",
                    @"(\d+)\s+" + context,
                    @"\b(\d+)\b"
                };

                foreach (var pattern in patterns)
                {
                    var match = Regex.Match(message, pattern, RegexOptions.IgnoreCase);
                    if (match.Success && int.TryParse(match.Groups[1].Value, out var count))
                    {
                        return Math.Max(1, count);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error extracting count from message");
            }

            return 1;
        }

        private void FinalizeBusinessMetricsSummary(SystemHealthSummary healthSummary)
        {
            _logger.LogDebug("Business metrics finalized");
        }

        private void DetermineOverallStatusWithBusinessMetrics(SystemHealthSummary healthSummary, List<ErrorPattern> allPatterns)
        {
            bool hasCriticalErrors = allPatterns.Any(p => p.Severity == "Critical" && p.Count > 0);
            bool hasHighSeverityErrors = allPatterns.Any(p => p.Severity == "High" && p.Count > 0);
            bool hasUserImpactingErrors = allPatterns.Any(p => p.IsUserImpacting && p.Count > 0);

            var businessIssuesCount = 0;
            if (healthSummary.TotalKeywordReports == 0) businessIssuesCount++;
            if (healthSummary.TotalSqsConversions == 0) businessIssuesCount++;
            if (healthSummary.TotalSqsTraffics == 0) businessIssuesCount++;

            double healthyPercentage = healthSummary.TotalServices > 0 ?
                (double)(healthSummary.HealthyServices + healthSummary.ActiveServices) / healthSummary.TotalServices * 100 : 0;

            double runningPercentage = healthSummary.TotalServices > 0 ?
                (double)healthSummary.RunningServices / healthSummary.TotalServices * 100 : 0;

            if (hasCriticalErrors || healthSummary.CriticalServices > 0)
            {
                healthSummary.OverallStatus = "Critical";
            }
            else if (businessIssuesCount >= 3)
            {
                healthSummary.OverallStatus = "Critical";
                _logger.LogWarning("System status set to Critical due to all business metrics being zero");
            }
            else if ((hasHighSeverityErrors && hasUserImpactingErrors) || healthSummary.TotalErrors > 5)
            {
                healthSummary.OverallStatus = "Error";
            }
            else if (businessIssuesCount >= 2)
            {
                healthSummary.OverallStatus = "Error";
                _logger.LogWarning("System status set to Error due to multiple business metrics being zero");
            }
            else if (hasHighSeverityErrors || healthSummary.TotalErrors > 0)
            {
                healthSummary.OverallStatus = "Warning";
            }
            else if (businessIssuesCount >= 1)
            {
                healthSummary.OverallStatus = "Warning";
                _logger.LogWarning("System status set to Warning due to business metric being zero");
            }
            else if (healthSummary.WarningServices > 0 || healthSummary.TotalWarnings > 3)
            {
                healthSummary.OverallStatus = "Warning";
            }
            else if (healthSummary.StaleServices > 0 && healthSummary.StaleServices >= healthSummary.TotalServices / 2)
            {
                healthSummary.OverallStatus = "Stale";
            }
            else if (healthyPercentage >= 90 && businessIssuesCount == 0)
            {
                healthSummary.OverallStatus = "Healthy";
            }
            else if (healthyPercentage >= 70)
            {
                healthSummary.OverallStatus = "Good";
            }
            else if (runningPercentage >= 50)
            {
                healthSummary.OverallStatus = "Degraded";
            }
            else if (healthSummary.StoppedServices == healthSummary.TotalServices)
            {
                healthSummary.OverallStatus = "Stopped";
            }
            else if (healthSummary.InactiveServices > 0 && healthSummary.InactiveServices >= healthSummary.TotalServices / 2)
            {
                healthSummary.OverallStatus = "Inactive";
            }
            else
            {
                healthSummary.OverallStatus = "Unknown";
            }

            _logger.LogInformation("Overall status: {Status}", healthSummary.OverallStatus);
        }
    }
}