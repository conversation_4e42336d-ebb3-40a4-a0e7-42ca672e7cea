@echo off
echo ============================================
echo Building LSB Telemetry Service Installer (Auto Dependencies)
echo ============================================

set SERVICE_PATH=D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service
set PUBLISH_PATH=%SERVICE_PATH%\bin\Release\net6.0\win-x64\publish

echo Step 1: Clean previous builds...
if exist "%PUBLISH_PATH%" rmdir /s /q "%PUBLISH_PATH%"
if exist "ServiceFiles.wxs" del "ServiceFiles.wxs"
if exist "*.wixobj" del "*.wixobj"
if exist "*.msi" del "*.msi"

echo Step 2: Publishing .NET Service with self-contained deployment...
dotnet publish "%SERVICE_PATH%\LSB.LogMonitor.Service.csproj" -c Release -r win-x64 --self-contained true -o "%PUBLISH_PATH%"
if %ERRORLEVEL% neq 0 (
    echo ERROR: Failed to publish .NET service
    pause
    exit /b 1
)

echo Step 3: Auto-generating WiX components using Heat.exe...
heat dir "%PUBLISH_PATH%" -cg ServiceFiles -gg -scom -sreg -sfrag -srd -dr INSTALLFOLDER -out ServiceFiles.wxs -var var.PublishPath
if %ERRORLEVEL% neq 0 (
    echo ERROR: Failed to generate WiX components
    pause
    exit /b 1
)

echo Step 4: Compiling WiX sources...
candle Product.wxs ServiceFiles.wxs -dPublishPath="%PUBLISH_PATH%"
if %ERRORLEVEL% neq 0 (
    echo ERROR: Failed to compile WiX source
    pause
    exit /b 1
)

echo Step 5: Creating MSI installer...
light Product.wixobj ServiceFiles.wixobj -out LSBTelemetryService.msi
if %ERRORLEVEL% neq 0 (
    echo ERROR: Failed to create MSI
    pause
    exit /b 1
)

echo ============================================
echo SUCCESS: LSBTelemetryService.msi created!
echo ============================================

echo Cleaning up...
del *.wixobj 2>nul
del ServiceFiles.wxs 2>nul

echo Build completed successfully!
echo.
echo To install: Run LSBTelemetryService.msi as Administrator
echo To uninstall: Use Control Panel or: msiexec /x LSBTelemetryService.msi
pause
