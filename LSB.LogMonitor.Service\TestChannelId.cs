using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using LSB.LogMonitor.Services;

namespace LSB.LogMonitor.Service
{
    public static class TestChannelId
    {
        public static async Task RunTest()
        {
            Console.WriteLine("🔧 Testing ChannelId from Config...");
            
            // Create a simple logger
            using var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
            var logger = loggerFactory.CreateLogger<ConfigService>();
            
            // Create ConfigService
            var configService = new ConfigService(logger);
            
            try
            {
                // Test GetAccNameAsync
                var accName = await configService.GetAccNameAsync();
                Console.WriteLine($"✅ AccName from config: {accName}");
                
                // Test GetChannelIdAsync - THIS IS THE NEW FEATURE
                var channelId = await configService.GetChannelIdAsync();
                Console.WriteLine($"✅ ChannelId from config: {channelId ?? "Not configured"}");
                
                // Test GetConfigAsync
                var config = await configService.GetConfigAsync();
                Console.WriteLine($"✅ Full Config loaded:");
                Console.WriteLine($"   - AccName: {config.AccName}");
                Console.WriteLine($"   - MachineName: {config.MachineName}");
                Console.WriteLine($"   - ChannelId: {config.ChannelId}");
                Console.WriteLine($"   - IsEnabled: {config.IsEnabled}");
                Console.WriteLine($"   - Version: {config.Version}");
                Console.WriteLine($"   - LastUpdated: {config.LastUpdated}");
                
                Console.WriteLine();
                Console.WriteLine("🎉 NEW FEATURE WORKING:");
                Console.WriteLine("   - Users can now add ChannelId to logmonitor.config");
                Console.WriteLine("   - No need to modify appsettings.json");
                Console.WriteLine("   - TelegramService will use ChannelId from config file");
                
                Console.WriteLine("✅ ChannelId test completed successfully!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ ChannelId test failed: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                throw;
            }
        }
    }
}
