using LSB.LogMonitor.Services;
using LSB.LogMonitor.Service.Configuration;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System.Reflection;
using System.IO;

// *** FIX WORKING DIRECTORY FOR WINDOWS SERVICE ***
try
{
    string exePath = Assembly.GetExecutingAssembly().Location;
    string exeDir = Path.GetDirectoryName(exePath);
    Directory.SetCurrentDirectory(exeDir);
    Console.WriteLine($"Working Directory set to: {Directory.GetCurrentDirectory()}");
}
catch (Exception ex)
{
    Console.WriteLine($"Warning: Could not set working directory: {ex.Message}");
}

// KHÔNG DÙNG CreateDefaultBuilder - tự build từ đầu
var builder = new HostBuilder()
    .UseWindowsService(options =>
    {
        options.ServiceName = "LSB Log Monitor Service";
    })
    .ConfigureAppConfiguration((context, config) =>
    {
        // ADD CONFIGURATION LOADING
        config.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
        config.AddJsonFile($"appsettings.{context.HostingEnvironment.EnvironmentName}.json", optional: true);
        config.AddEnvironmentVariables();
    })
    .ConfigureServices((context, services) =>
    {
        // Configure options
        services.Configure<LogMonitorOptions>(context.Configuration.GetSection(LogMonitorOptions.SectionName));

        services.AddSingleton<ILogService, LogService>();
        services.AddSingleton<ITelegramService, TelegramService>();
        services.AddHostedService<LogMonitorWorker>();
    })
    .ConfigureLogging(logging =>
    {
        logging.ClearProviders(); // Remove console logging for service
        logging.AddEventLog(); // Add Windows Event Log
        logging.SetMinimumLevel(LogLevel.Information);
    });

// Check for test argument
if (args.Length > 0 && args[0].Equals("--test", StringComparison.OrdinalIgnoreCase))
{
    Console.WriteLine("Running in test mode...");
    await LSB.LogMonitor.Service.QuickTest.RunTest();
    return;
}

var host = builder.Build();
Console.WriteLine("LSB Log Monitor Service starting...");

try
{
    // *** IMPORTANT: Use RunAsync instead of Run to prevent blocking ***
    await host.RunAsync();
}
catch (Exception ex)
{
    Console.WriteLine($"Service error: {ex.Message}");
    throw;
}
finally
{
    Console.WriteLine("LSB Log Monitor Service stopped");
}