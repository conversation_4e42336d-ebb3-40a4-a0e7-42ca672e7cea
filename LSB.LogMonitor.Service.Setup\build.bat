﻿@echo off
echo ============================================
echo Building LSB Telemetry Service Installer
echo ============================================

set SERVICE_PATH=D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service

echo Step 1: Publishing .NET Service with all dependencies...
dotnet publish "%SERVICE_PATH%\LSB.LogMonitor.Service.csproj" -c Release -r win-x64 --self-contained true -o "%SERVICE_PATH%\bin\Release\net6.0\win-x64\publish"
if %ERRORLEVEL% neq 0 (
    echo ERROR: Failed to publish .NET service
    pause
    exit /b 1
)

echo Step 2: Compiling WiX installer...
candle Product.wxs
if %ERRORLEVEL% neq 0 (
    echo ERROR: Failed to compile WiX source
    pause
    exit /b 1
)

echo Step 3: Creating MSI installer...
light Product.wixobj -out LSBTelemetryService.msi
if %ERRORLEVEL% neq 0 (
    echo ERROR: Failed to create MSI
    pause
    exit /b 1
)

echo ============================================
echo SUCCESS: LSBTelemetryService.msi created!
echo ============================================

echo Cleaning up...
del *.wixobj 2>nul

echo Build completed successfully!
echo.
echo To install: Run LSBTelemetryService.msi as Administrator
echo To uninstall: Use Control Panel or: msiexec /x LSBTelemetryService.msi
pause