﻿using LSB.LogMonitor.Service.Models;

namespace LSB.LogMonitor.Services
{
    public interface ILogService
    {
        Task<LogSummaryResponse> GenerateLogSummaryAsync(DateTime date, string clientName, CancellationToken cancellationToken = default);
        Task<bool> LogsExistAsync(DateTime date, string clientName, CancellationToken cancellationToken = default);
        Task<string[]> GetAvailableClientNamesAsync(CancellationToken cancellationToken = default);
    }
}
