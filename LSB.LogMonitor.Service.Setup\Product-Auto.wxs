<?xml version="1.0" encoding="UTF-8"?>
<Wix xmlns="http://schemas.microsoft.com/wix/2006/wi">
	<Product Id="*"
			 Name="LSB Telemetry Service"
			 Language="1033"
			 Codepage="1252"
			 Version="1.0.0.0"
			 Manufacturer="LSB"
			 UpgradeCode="{EB8A2174-7977-45CB-8EDB-936FBC25378E}">
		<Package InstallerVersion="200"
				 Compressed="yes"
				 InstallScope="perMachine"
				 Platform="x64" />
		<Media Id="1" Cabinet="files.cab" EmbedCab="yes" />

		<!-- AUTO UPDATE SUPPORT -->
		<MajorUpgrade DowngradeErrorMessage="A newer version is already installed."
					  Schedule="afterInstallInitialize" />

		<Directory Id="TARGETDIR" Name="SourceDir">
			<Directory Id="ProgramFiles64Folder">
				<Directory Id="CompanyFolder" Name="LSB">
					<Directory Id="INSTALLFOLDER" Name="TelemetryService" />
				</Directory>
			</Directory>
		</Directory>

		<Feature Id="Complete" Level="1">
			<ComponentGroupRef Id="ServiceFiles" />
			<ComponentRef Id="ServiceComponent" />
		</Feature>

		<!-- Service Component -->
		<DirectoryRef Id="INSTALLFOLDER">
			<Component Id="ServiceComponent" Guid="{4DD20919-9DCB-4405-88A7-A5A9F57972CD}" Win64="yes">
				<!-- Service Installation -->
				<ServiceInstall Id="InstallService"
								Name="LSBTelemetryService"
								DisplayName="LSB Telemetry Service"
								Description="LSB Telemetry Service - System monitoring"
								Type="ownProcess"
								Start="auto"
								ErrorControl="normal"
								Account="LocalSystem" />

				<ServiceControl Id="ServiceControl"
								Name="LSBTelemetryService"
								Stop="both"
								Remove="uninstall"
								Wait="yes" />
			</Component>
		</DirectoryRef>

		<!-- Custom Actions to fix service issues -->
		<CustomAction Id="SetServiceTimeout"
					  Directory="INSTALLFOLDER"
					  ExeCommand='reg add "HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control" /v "ServicesPipeTimeout" /t REG_DWORD /d "180000" /f'
					  Execute="deferred"
					  Impersonate="no" />

		<CustomAction Id="SetServiceConfig"
					  Directory="INSTALLFOLDER"
					  ExeCommand='sc config LSBTelemetryService start= auto obj= LocalSystem'
					  Execute="deferred"
					  Impersonate="no" />

		<CustomAction Id="SetServiceFailureActions"
					  Directory="INSTALLFOLDER"
					  ExeCommand='sc failure LSBTelemetryService reset= 86400 actions= restart/30000/restart/60000/restart/60000'
					  Execute="deferred"
					  Impersonate="no" />

		<InstallExecuteSequence>
			<Custom Action="SetServiceTimeout" After="InstallServices">NOT Installed</Custom>
			<Custom Action="SetServiceConfig" After="SetServiceTimeout">NOT Installed</Custom>
			<Custom Action="SetServiceFailureActions" After="SetServiceConfig">NOT Installed</Custom>
		</InstallExecuteSequence>
	</Product>
</Wix>
